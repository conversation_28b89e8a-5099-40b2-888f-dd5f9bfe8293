-- Migration: Add contact person reference to projects table
-- Date: 2025-01-22
-- Description: Adds an optional contact_person_id field to the projects table to track the primary contact for each project

-- Add the contact_person_id column to the projects table
ALTER TABLE projects 
ADD COLUMN contact_person_id UUID REFERENCES contact_persons(id) ON DELETE SET NULL;

-- Add a comment to document the column
COMMENT ON COLUMN projects.contact_person_id IS 'Reference to the primary contact person for this project (optional)';

-- Create an index for faster lookups
CREATE INDEX idx_projects_contact_person_id ON projects(contact_person_id) WHERE contact_person_id IS NOT NULL;

-- Update the updated_at timestamp for any existing records (optional)
-- This ensures the updated_at field reflects the schema change
UPDATE projects SET updated_at = NOW() WHERE updated_at IS NOT NULL;
