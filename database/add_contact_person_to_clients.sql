-- Migration: Add contact person field to clients table
-- Date: 2025-01-21
-- Description: Adds an optional contact person name field to the clients table

-- Add the contact_person column to the clients table
ALTER TABLE clients 
ADD COLUMN contact_person TEXT;

-- Add a comment to document the column
COMMENT ON COLUMN clients.contact_person IS 'Name of the primary contact person for the client (optional)';

-- Create an index for faster searches on contact person (optional but recommended)
CREATE INDEX idx_clients_contact_person ON clients(contact_person) WHERE contact_person IS NOT NULL;

-- Update the updated_at timestamp for any existing records (optional)
-- This ensures the updated_at field reflects the schema change
UPDATE clients SET updated_at = NOW() WHERE updated_at IS NOT NULL;
