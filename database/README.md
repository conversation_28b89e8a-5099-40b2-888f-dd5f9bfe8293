# Cymatics Database Setup

This directory contains the database schema and setup files for the Cymatics drone service management application.

## Files

- `schema.sql` - Complete database schema with tables, indexes, triggers, and RLS policies
- `seed.sql` - Sample data for testing and development
- `README.md` - This file with setup instructions

## Database Schema Overview

### Core Tables

1. **users** - User profiles extending Supabase auth.users
2. **clients** - Client information and contact details
3. **projects** - Drone service projects with billing information
4. **shoots** - Individual drone shoots/sessions
5. **expenses** - Project and operational expenses
6. **tasks** - Task management and assignments
7. **payments** - Payment tracking and history
8. **notifications** - In-app notifications
9. **activity_logs** - User activity tracking

### Key Features

- **Row Level Security (RLS)** - Comprehensive security policies based on user roles
- **Role-based Access Control** - Admin, Manager, Pilot, and Editor roles
- **Automatic Triggers** - Updated timestamps and calculated fields
- **Comprehensive Indexing** - Optimized for common query patterns
- **Data Integrity** - Foreign key constraints and check constraints

## Setup Instructions

### 1. Supabase Project Setup

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Note your project URL and anon key
3. Update your `.env.local` file with the credentials

### 2. Database Schema Setup

1. Open the Supabase SQL Editor in your project dashboard
2. Copy and paste the contents of `schema.sql`
3. Execute the script to create all tables, indexes, and policies

### 3. Sample Data (Optional)

1. In the Supabase SQL Editor, copy and paste the contents of `seed.sql`
2. Execute the script to insert sample data
3. Note: You'll need to create test users through the Supabase Auth interface first

### 4. Environment Variables

Update your `.env.local` file with your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## User Roles and Permissions

### Admin
- Full access to all features
- User management
- System configuration
- All data access

### Manager
- Project and client management
- Financial data access
- Team management
- Cannot manage users

### Pilot
- View assigned projects and shoots
- Update shoot status and notes
- Manage personal expenses
- View assigned tasks

### Editor
- Post-processing tasks
- File management
- Project asset organization
- Limited project access

## Security Considerations

- All tables have Row Level Security enabled
- Policies enforce role-based access control
- Sensitive operations require elevated permissions
- Activity logging for audit trails

## Data Relationships

```
clients (1) -> (many) projects
projects (1) -> (many) shoots
projects (1) -> (many) payments
users (1) -> (many) expenses
users (1) -> (many) tasks
projects (1) -> (many) tasks
shoots (1) -> (many) tasks
```

## Maintenance

### Regular Tasks
- Monitor activity logs for unusual patterns
- Review and archive old notifications
- Backup critical project data
- Update user roles as needed

### Performance Monitoring
- Check query performance on large datasets
- Monitor index usage
- Review RLS policy performance
- Optimize based on usage patterns

## Troubleshooting

### Common Issues

1. **RLS Policy Errors**
   - Ensure user has proper role assigned
   - Check policy conditions match user context

2. **Foreign Key Violations**
   - Verify referenced records exist
   - Check cascade delete behavior

3. **Permission Denied**
   - Confirm user authentication
   - Verify role-based permissions

### Support

For database-related issues:
1. Check Supabase logs in the dashboard
2. Review RLS policies for access issues
3. Verify data integrity constraints
4. Contact support with specific error messages
