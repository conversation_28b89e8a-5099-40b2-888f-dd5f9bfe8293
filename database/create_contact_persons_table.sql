-- Migration: Create contact_persons table for multiple contacts per client
-- Date: 2025-01-21
-- Description: Creates a separate table to store multiple contact persons for each client

-- Create contact_persons table for multiple contacts per client
CREATE TABLE contact_persons (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  client_id UUID NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  phone TEXT,
  email TEXT,
  designation TEXT,
  is_primary BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add comments
COMMENT ON TABLE contact_persons IS 'Contact persons associated with clients';
COMMENT ON COLUMN contact_persons.client_id IS 'Reference to the client this contact belongs to';
COMMENT ON COLUMN contact_persons.name IS 'Name of the contact person';
COMMENT ON COLUMN contact_persons.phone IS 'Phone number of the contact person';
COMMENT ON COLUMN contact_persons.email IS 'Email address of the contact person';
COMMENT ON COLUMN contact_persons.designation IS 'Job title or designation of the contact person';
COMMENT ON COLUMN contact_persons.is_primary IS 'Whether this is the primary contact for the client';

-- Create indexes
CREATE INDEX idx_contact_persons_client_id ON contact_persons(client_id);
CREATE INDEX idx_contact_persons_name ON contact_persons(name);
CREATE INDEX idx_contact_persons_phone ON contact_persons(phone) WHERE phone IS NOT NULL;
CREATE INDEX idx_contact_persons_primary ON contact_persons(client_id, is_primary) WHERE is_primary = true;

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_contact_persons_updated_at BEFORE UPDATE ON contact_persons FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS
ALTER TABLE contact_persons ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (same as clients table)
CREATE POLICY "Users can view contact_persons" ON contact_persons FOR SELECT USING (true);
CREATE POLICY "Users can insert contact_persons" ON contact_persons FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can update contact_persons" ON contact_persons FOR UPDATE USING (true);
CREATE POLICY "Users can delete contact_persons" ON contact_persons FOR DELETE USING (true);

-- Remove the single contact_person column from clients table
ALTER TABLE clients DROP COLUMN IF EXISTS contact_person;

-- Drop the index we created earlier
DROP INDEX IF EXISTS idx_clients_contact_person;
