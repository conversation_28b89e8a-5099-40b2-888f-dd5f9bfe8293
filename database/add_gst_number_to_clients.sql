-- Migration: Add GST number field to clients table
-- Date: 2025-01-21
-- Description: Adds an optional GST number field to the clients table for tax purposes

-- Add the gst_number column to the clients table
ALTER TABLE clients 
ADD COLUMN gst_number TEXT;

-- Add a comment to document the column
COMMENT ON COLUMN clients.gst_number IS 'GST registration number for the client (optional)';

-- Create an index for faster searches on GST number (optional but recommended)
CREATE INDEX idx_clients_gst_number ON clients(gst_number) WHERE gst_number IS NOT NULL;

-- Update the updated_at timestamp for any existing records (optional)
-- This ensures the updated_at field reflects the schema change
UPDATE clients SET updated_at = NOW() WHERE updated_at IS NOT NULL;
