/**
 * Utility functions for Google Maps integration
 */

export interface LocationInfo {
  name?: string
  address?: string
  coordinates?: {
    lat: number
    lng: number
  }
}

/**
 * Extract location information from Google Maps URL
 */
export function parseGoogleMapsUrl(url: string): LocationInfo | null {
  if (!url || !url.includes('maps.google.com') && !url.includes('goo.gl/maps')) {
    return null
  }

  try {
    const urlObj = new URL(url)
    const result: LocationInfo = {}

    // Handle different Google Maps URL formats
    
    // Format 1: https://maps.google.com/maps?q=location+name
    if (urlObj.searchParams.has('q')) {
      const query = urlObj.searchParams.get('q')
      if (query) {
        result.name = decodeURIComponent(query.replace(/\+/g, ' '))
      }
    }

    // Format 2: https://maps.google.com/maps/place/Location+Name/@lat,lng,zoom
    const pathMatch = urlObj.pathname.match(/\/place\/([^\/]+)/)
    if (pathMatch) {
      result.name = decodeURIComponent(pathMatch[1].replace(/\+/g, ' '))
    }

    // Extract coordinates from various formats
    // Format: @lat,lng,zoom or ll=lat,lng
    const coordsMatch = url.match(/@(-?\d+\.?\d*),(-?\d+\.?\d*)/) || 
                       url.match(/ll=(-?\d+\.?\d*),(-?\d+\.?\d*)/)
    
    if (coordsMatch) {
      result.coordinates = {
        lat: parseFloat(coordsMatch[1]),
        lng: parseFloat(coordsMatch[2])
      }
    }

    // If we have coordinates but no name, try to extract from URL path
    if (result.coordinates && !result.name) {
      const segments = urlObj.pathname.split('/').filter(Boolean)
      const placeIndex = segments.findIndex(segment => segment === 'place')
      if (placeIndex !== -1 && segments[placeIndex + 1]) {
        result.name = decodeURIComponent(segments[placeIndex + 1].replace(/\+/g, ' '))
      }
    }

    return Object.keys(result).length > 0 ? result : null
  } catch (error) {
    console.error('Error parsing Google Maps URL:', error)
    return null
  }
}

/**
 * Fetch location details using Google Maps Geocoding API (if available)
 * This is a placeholder for future implementation with actual API key
 */
export async function fetchLocationFromCoordinates(
  lat: number, 
  lng: number
): Promise<string | null> {
  // This would require a Google Maps API key
  // For now, we'll return a formatted coordinate string
  return `${lat.toFixed(6)}, ${lng.toFixed(6)}`
}

/**
 * Generate a clean location string from parsed data
 */
export function formatLocationFromMapsData(locationInfo: LocationInfo): string {
  if (locationInfo.name) {
    return locationInfo.name
  }
  
  if (locationInfo.coordinates) {
    return `${locationInfo.coordinates.lat.toFixed(6)}, ${locationInfo.coordinates.lng.toFixed(6)}`
  }
  
  if (locationInfo.address) {
    return locationInfo.address
  }
  
  return ''
}

/**
 * Validate if a URL is a valid Google Maps link
 */
export function isValidGoogleMapsUrl(url: string): boolean {
  if (!url) return false
  
  try {
    const urlObj = new URL(url)
    return urlObj.hostname.includes('maps.google.com') || 
           urlObj.hostname.includes('goo.gl') ||
           urlObj.hostname.includes('maps.app.goo.gl')
  } catch {
    return false
  }
}

/**
 * Extract location name from various Google Maps URL formats
 */
export function extractLocationName(url: string): string {
  const locationInfo = parseGoogleMapsUrl(url)
  return locationInfo ? formatLocationFromMapsData(locationInfo) : ''
}

/**
 * Format a location into a Google Maps URL
 */
export function formatGoogleMapsUrl(location: string): string {
  if (!location) return '';
  const encodedLocation = encodeURIComponent(location.trim());
  return `https://www.google.com/maps/search/?api=1&query=${encodedLocation}`;
}

export function getCurrentLocationUrl(): string {
  return 'https://www.google.com/maps/search/?api=1&query=Current+Location';
}

export function getDirectionsUrl(destination: string, origin?: string): string {
  let url = `https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(destination)}`;
  if (origin) {
    url += `&origin=${encodeURIComponent(origin)}`;
  }
  return url;
}
