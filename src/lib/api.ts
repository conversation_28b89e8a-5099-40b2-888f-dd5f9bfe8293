import { createClientSupabaseClient } from './auth'
import { getUpdatedProjectAmounts } from './project-calculations'
import type {
  Client,
  ContactPerson,
  Project,
  Shoot,
  Expense,
  Task,
  Payment,
  Notification,
  ActivityLog,
  CreateClientForm,
  CreateContactPersonForm,
  CreateProjectForm,
  CreateShootForm,
  CreateExpenseForm,
  CreateTaskForm,
  DashboardStats,
  PaginatedResponse
} from '@/types'

const supabase = createClientSupabaseClient()

// Clients API
export const clientsApi = {
  async getAll(): Promise<Client[]> {
    const { data, error } = await supabase
      .from('clients')
      .select(`
        *,
        contact_persons (*)
      `)
      .order('name')

    if (error) throw error
    return data || []
  },

  async getById(id: string): Promise<Client | null> {
    const { data, error } = await supabase
      .from('clients')
      .select(`
        *,
        contact_persons (*)
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  async create(client: CreateClientForm): Promise<Client> {
    const { data, error } = await supabase
      .from('clients')
      .insert(client)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async update(id: string, updates: Partial<CreateClientForm>): Promise<Client> {
    const { data, error } = await supabase
      .from('clients')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('clients')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Contact Persons API
export const contactPersonsApi = {
  async getByClientId(clientId: string): Promise<ContactPerson[]> {
    const { data, error } = await supabase
      .from('contact_persons')
      .select('*')
      .eq('client_id', clientId)
      .order('is_primary', { ascending: false })
      .order('name')

    if (error) throw error
    return data || []
  },

  async create(contactPerson: CreateContactPersonForm & { client_id: string }): Promise<ContactPerson> {
    const { data, error } = await supabase
      .from('contact_persons')
      .insert(contactPerson)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async update(id: string, contactPerson: Partial<CreateContactPersonForm>): Promise<ContactPerson> {
    const { data, error } = await supabase
      .from('contact_persons')
      .update(contactPerson)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('contact_persons')
      .delete()
      .eq('id', id)

    if (error) throw error
  },

  async setPrimary(id: string, clientId: string): Promise<void> {
    // First, unset all primary contacts for this client
    await supabase
      .from('contact_persons')
      .update({ is_primary: false })
      .eq('client_id', clientId)

    // Then set the specified contact as primary
    const { error } = await supabase
      .from('contact_persons')
      .update({ is_primary: true })
      .eq('id', id)

    if (error) throw error
  }
}

// Projects API
export const projectsApi = {
  async getAll(): Promise<Project[]> {
    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        client:clients(*),
        contact_person:contact_persons(*),
        shoots:shoots(count)
      `)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  },

  async getById(id: string): Promise<Project | null> {
    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        client:clients(*),
        contact_person:contact_persons(*),
        shoots:shoots(*),
        payments:payments(*),
        tasks:tasks(*)
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data
  },

  async create(project: CreateProjectForm): Promise<Project> {
    const { data, error } = await supabase
      .from('projects')
      .insert({
        ...project,
        total_amount: 0, // Will be calculated from shoots
        gst_inclusive: false, // Will be determined by client GST status
        amount_received: 0,
        amount_pending: 0
      })
      .select(`
        *,
        client:clients(*),
        contact_person:contact_persons(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async update(id: string, updates: Partial<CreateProjectForm>): Promise<Project> {
    const { data, error } = await supabase
      .from('projects')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        client:clients(*),
        contact_person:contact_persons(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', id)

    if (error) throw error
  },

  async recalculateProjectTotal(projectId: string): Promise<Project> {
    // Get project with client and shoots
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        *,
        client:clients(*),
        shoots:shoots(*)
      `)
      .eq('id', projectId)
      .single()

    if (projectError) throw projectError
    if (!project) throw new Error('Project not found')

    // Calculate new amounts
    const updatedAmounts = getUpdatedProjectAmounts(
      project.shoots || [],
      project.client,
      project.amount_received
    )

    // Update project with new amounts
    const { data: updatedProject, error: updateError } = await supabase
      .from('projects')
      .update(updatedAmounts)
      .eq('id', projectId)
      .select(`
        *,
        client:clients(*),
        contact_person:contact_persons(*)
      `)
      .single()

    if (updateError) throw updateError
    return updatedProject
  }
}

// Shoots API
export const shootsApi = {
  async getAll(): Promise<Shoot[]> {
    const { data, error } = await supabase
      .from('shoots')
      .select(`
        *,
        project:projects(*),
        pilot:users(*)
      `)
      .order('scheduled_date')

    if (error) throw error
    return data || []
  },

  async getUpcoming(): Promise<Shoot[]> {
    const { data, error } = await supabase
      .from('shoots')
      .select(`
        *,
        project:projects(*),
        pilot:users(*)
      `)
      .gte('scheduled_date', new Date().toISOString())
      .eq('status', 'scheduled')
      .order('scheduled_date')
      .limit(10)

    if (error) throw error
    return data || []
  },

  async create(shoot: CreateShootForm): Promise<Shoot> {
    const { data, error } = await supabase
      .from('shoots')
      .insert(shoot)
      .select(`
        *,
        project:projects(*),
        pilot:users(*)
      `)
      .single()

    if (error) throw error

    // Recalculate project total
    await projectsApi.recalculateProjectTotal(shoot.project_id)

    return data
  },

  async update(id: string, updates: Partial<CreateShootForm>): Promise<Shoot> {
    const { data, error } = await supabase
      .from('shoots')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        project:projects(*),
        pilot:users(*)
      `)
      .single()

    if (error) throw error

    // Recalculate project total
    await projectsApi.recalculateProjectTotal(data.project_id)

    return data
  },

  async delete(id: string): Promise<void> {
    // Get the shoot to find the project_id before deleting
    const { data: shoot, error: getError } = await supabase
      .from('shoots')
      .select('project_id')
      .eq('id', id)
      .single()

    if (getError) throw getError

    const { error } = await supabase
      .from('shoots')
      .delete()
      .eq('id', id)

    if (error) throw error

    // Recalculate project total
    if (shoot) {
      await projectsApi.recalculateProjectTotal(shoot.project_id)
    }
  }
}

// Expenses API
export const expensesApi = {
  async getAll(): Promise<Expense[]> {
    const { data, error } = await supabase
      .from('expenses')
      .select(`
        *,
        project:projects(*),
        user:users(*)
      `)
      .order('date', { ascending: false })

    if (error) throw error
    return data || []
  },

  async getByProjectId(projectId: string): Promise<Expense[]> {
    const { data, error } = await supabase
      .from('expenses')
      .select(`
        *,
        project:projects(*),
        user:users(*)
      `)
      .eq('project_id', projectId)
      .order('date', { ascending: false })

    if (error) throw error
    return data || []
  },

  async create(expense: CreateExpenseForm): Promise<Expense> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    const { data, error } = await supabase
      .from('expenses')
      .insert({
        ...expense,
        user_id: user.id
      })
      .select(`
        *,
        project:projects(*),
        user:users(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async update(id: string, updates: Partial<CreateExpenseForm>): Promise<Expense> {
    const { data, error } = await supabase
      .from('expenses')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        project:projects(*),
        user:users(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('expenses')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Tasks API
export const tasksApi = {
  async getAll(): Promise<Task[]> {
    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        assigned_user:users(*),
        project:projects(*),
        shoot:shoots(*)
      `)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  },

  async getMyTasks(): Promise<Task[]> {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) throw new Error('Not authenticated')

    const { data, error } = await supabase
      .from('tasks')
      .select(`
        *,
        assigned_user:users(*),
        project:projects(*),
        shoot:shoots(*)
      `)
      .eq('assigned_to', user.id)
      .order('created_at', { ascending: false })

    if (error) throw error
    return data || []
  },

  async create(task: CreateTaskForm): Promise<Task> {
    const { data, error } = await supabase
      .from('tasks')
      .insert(task)
      .select(`
        *,
        assigned_user:users(*),
        project:projects(*),
        shoot:shoots(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async update(id: string, updates: Partial<CreateTaskForm>): Promise<Task> {
    const { data, error } = await supabase
      .from('tasks')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        assigned_user:users(*),
        project:projects(*),
        shoot:shoots(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Payments API
export const paymentsApi = {
  async getAll(): Promise<Payment[]> {
    const { data, error } = await supabase
      .from('payments')
      .select(`
        *,
        project:projects(*)
      `)
      .order('payment_date', { ascending: false })

    if (error) throw error
    return data || []
  },

  async create(payment: Omit<Payment, 'id' | 'created_at' | 'updated_at'>): Promise<Payment> {
    const { data, error } = await supabase
      .from('payments')
      .insert(payment)
      .select(`
        *,
        project:projects(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async update(id: string, updates: Partial<Omit<Payment, 'id' | 'created_at' | 'updated_at'>>): Promise<Payment> {
    const { data, error } = await supabase
      .from('payments')
      .update(updates)
      .eq('id', id)
      .select(`
        *,
        project:projects(*)
      `)
      .single()

    if (error) throw error
    return data
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('payments')
      .delete()
      .eq('id', id)

    if (error) throw error
  }
}

// Dashboard API
export const dashboardApi = {
  async getStats(): Promise<DashboardStats> {
    // Get all stats in parallel
    const [
      projectsResult,
      shootsResult,
      tasksResult,
      paymentsResult
    ] = await Promise.all([
      supabase.from('projects').select('status, total_amount'),
      supabase.from('shoots').select('status, scheduled_date'),
      supabase.from('tasks').select('status, due_date'),
      supabase.from('payments').select('amount, payment_date')
    ])

    if (projectsResult.error) throw projectsResult.error
    if (shootsResult.error) throw shootsResult.error
    if (tasksResult.error) throw tasksResult.error
    if (paymentsResult.error) throw paymentsResult.error

    const projects = projectsResult.data || []
    const shoots = shootsResult.data || []
    const tasks = tasksResult.data || []
    const payments = paymentsResult.data || []

    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)

    return {
      totalProjects: projects.length,
      activeProjects: projects.filter(p => p.status === 'active').length,
      completedShoots: shoots.filter(s => s.status === 'completed').length,
      pendingPayments: projects.filter(p => p.status === 'active').length, // Simplified
      totalRevenue: payments.reduce((sum, p) => sum + p.amount, 0),
      monthlyRevenue: payments
        .filter(p => new Date(p.payment_date) >= startOfMonth)
        .reduce((sum, p) => sum + p.amount, 0),
      upcomingShoots: shoots.filter(s => 
        s.status === 'scheduled' && new Date(s.scheduled_date) >= now
      ).length,
      overdueTasks: tasks.filter(t => 
        t.status !== 'completed' && 
        t.due_date && 
        new Date(t.due_date) < now
      ).length
    }
  }
}
