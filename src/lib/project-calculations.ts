import type { Project, Shoot, Client } from '@/types'

// GST rate (18% in India)
const GST_RATE = 0.18

export interface ProjectCalculation {
  subtotal: number
  gstAmount: number
  total: number
  hasGst: boolean
  expenses: number
  outsourcing: number
  profit: number
}

/**
 * Calculate project totals based on shoots, client GST status, expenses, and outsourcing costs
 */
export function calculateProjectTotal(
  shoots: Shoot[],
  client: Client,
  expenses: number = 0
): ProjectCalculation {
  // Calculate subtotal from all shoots
  const subtotal = shoots.reduce((sum, shoot) => sum + (shoot.amount || 0), 0)

  // Calculate outsourcing costs from all shoots
  const outsourcing = shoots.reduce((sum, shoot) => sum + (shoot.outsourcing_cost || 0), 0)

  // Calculate GST if client has GST registration
  const hasGst = client.has_gst
  const gstAmount = hasGst ? subtotal * GST_RATE : 0
  const total = subtotal + gstAmount

  // Calculate profit (total amount - expenses - outsourcing costs)
  const profit = total - expenses - outsourcing

  return {
    subtotal,
    gstAmount,
    total,
    hasGst,
    expenses,
    outsourcing,
    profit
  }
}

/**
 * Update project amounts in the database
 */
export function getUpdatedProjectAmounts(
  shoots: Shoot[],
  client: Client,
  amountReceived: number = 0,
  expenses: number = 0
): {
  total_amount: number
  gst_inclusive: boolean
  amount_received: number
  amount_pending: number
  expenses: number
  profit: number
} {
  const calculation = calculateProjectTotal(shoots, client, expenses)
  
  return {
    total_amount: calculation.total,
    gst_inclusive: calculation.hasGst,
    amount_received: amountReceived,
    amount_pending: calculation.total - amountReceived,
    expenses,
    profit: calculation.profit
  }
}

/**
 * Format currency for display
 */
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

/**
 * Calculate GST breakdown for display
 */
export function getGstBreakdown(
  subtotal: number,
  hasGst: boolean
): {
  subtotal: number
  gstRate: number
  gstAmount: number
  total: number
  formattedSubtotal: string
  formattedGstAmount: string
  formattedTotal: string
} {
  const gstAmount = hasGst ? subtotal * GST_RATE : 0
  const total = subtotal + gstAmount

  return {
    subtotal,
    gstRate: hasGst ? GST_RATE * 100 : 0, // Convert to percentage
    gstAmount,
    total,
    formattedSubtotal: formatCurrency(subtotal),
    formattedGstAmount: formatCurrency(gstAmount),
    formattedTotal: formatCurrency(total)
  }
}
