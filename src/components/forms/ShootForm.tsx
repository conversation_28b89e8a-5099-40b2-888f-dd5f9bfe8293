'use client'

import { useState, useEffect, useCallback } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useCreateShoot, useUpdateShoot, useProjects, useClients } from '@/hooks/useApi'
import { useAuth } from '@/contexts/AuthContext'
import { extractLocationFromMapsUrl } from '@/lib/maps-utils'
import { supabase } from '@/lib/supabase'
import toast from 'react-hot-toast'
import Link from 'next/link'
import type { Shoot, User, OutsourcingVendor } from '@/types'

const shootSchema = z.object({
  project_id: z.string().min(1, 'Project is required'),
  scheduled_date: z.string().min(1, 'Start date & time is required'),
  scheduled_end_date: z.string().optional(),
  pilot_id: z.string().optional(),
  amount: z.number().min(0, 'Amount must be positive').optional(),
  location: z.string().optional(),
  google_maps_link: z.string().optional(),
  notes: z.string().optional(),
  is_recurring: z.boolean(),
  recurring_pattern: z.enum(['weekly', 'monthly', 'quarterly']).optional(),
  // Vendor/Outsourcing fields
  is_outsourced: z.boolean(),
  vendor_id: z.string().optional(),
  outsourcing_cost: z.number().min(0, 'Outsourcing cost must be positive').optional(),

}).refine((data) => {
  if (data.scheduled_end_date && data.scheduled_date) {
    return new Date(data.scheduled_end_date) > new Date(data.scheduled_date)
  }
  return true
}, {
  message: 'End time must be after start time',
  path: ['scheduled_end_date'],
})

type ShootFormData = z.infer<typeof shootSchema>

interface ShootFormProps {
  shoot?: Shoot
  onSuccess?: (shoot: Shoot) => void
  onCancel?: () => void
  preselectedProjectId?: string
}

export function ShootForm({ shoot, onSuccess, onCancel, preselectedProjectId }: ShootFormProps) {
  const isEditing = !!shoot
  const { user } = useAuth()
  const { createShoot, loading: createLoading } = useCreateShoot()
  const { updateShoot, loading: updateLoading } = useUpdateShoot()
  const { data: projects, loading: projectsLoading } = useProjects()
  const loading = createLoading || updateLoading

  const [extractingLocation, setExtractingLocation] = useState(false)
  const [useProjectLocation, setUseProjectLocation] = useState(true)
  const [users, setUsers] = useState<User[]>([])
  const [loadingUsers, setLoadingUsers] = useState(false)
  const [vendors, setVendors] = useState<OutsourcingVendor[]>([])
  const [loadingVendors, setLoadingVendors] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
    getValues,
  } = useForm<ShootFormData>({
    resolver: zodResolver(shootSchema),
    defaultValues: shoot ? {
      project_id: shoot.project_id,
      scheduled_date: new Date(shoot.scheduled_date).toISOString().slice(0, 16),
      scheduled_end_date: shoot.scheduled_end_date ? new Date(shoot.scheduled_end_date).toISOString().slice(0, 16) : '',
      pilot_id: shoot.pilot_id || '',
      amount: shoot.amount || undefined,
      location: shoot.location || '',
      google_maps_link: shoot.google_maps_link || '',
      notes: shoot.notes || '',
      is_recurring: shoot.is_recurring,
      recurring_pattern: shoot.recurring_pattern || undefined,
      is_outsourced: shoot.is_outsourced || false,
      vendor_id: shoot.vendor_id || '',
      outsourcing_cost: shoot.outsourcing_cost || undefined,

    } : {
      project_id: preselectedProjectId || '',
      scheduled_date: '',
      scheduled_end_date: '',
      pilot_id: user?.role === 'pilot' ? user.id : '',
      amount: undefined,
      location: '',
      google_maps_link: '',
      notes: '',
      is_recurring: false,
      recurring_pattern: undefined,
      is_outsourced: false,
      vendor_id: '',
      outsourcing_cost: undefined,

    },
  })

  const watchIsRecurring = watch('is_recurring')
  const watchProjectId = watch('project_id')
  const watchGoogleMapsLink = watch('google_maps_link')
  const watchScheduledDate = watch('scheduled_date')
  const watchScheduledEndDate = watch('scheduled_end_date')
  const watchIsOutsourced = watch('is_outsourced')

  // Get selected project
  const selectedProject = projects?.find(p => p.id === watchProjectId)

  // Fetch users for pilot dropdown
  useEffect(() => {
    const fetchUsers = async () => {
      setLoadingUsers(true)
      try {
        const { data, error } = await supabase
          .from('users')
          .select('id, name, email, role')
          .order('name')

        if (error) throw error
        setUsers(data || [])
      } catch (error) {
        console.error('Error fetching users:', error)
        toast.error('Failed to load users')
      } finally {
        setLoadingUsers(false)
      }
    }

    fetchUsers()
  }, [])

  // Fetch vendors for outsourcing dropdown
  useEffect(() => {
    const fetchVendors = async () => {
      setLoadingVendors(true)
      try {
        const { data, error } = await supabase
          .from('outsourcing_vendors')
          .select('id, name, email, phone, specialization, is_active')
          .eq('is_active', true)
          .order('name')

        if (error) throw error
        setVendors(data || [])
      } catch (error) {
        console.error('Error fetching vendors:', error)
        toast.error('Failed to load vendors')
      } finally {
        setLoadingVendors(false)
      }
    }

    fetchVendors()
  }, [])

  // Custom debounce function for location extraction
  const debouncedExtractLocation = useCallback(
    (() => {
      let timeoutId: NodeJS.Timeout | null = null

      return (url: string, callback: (locationInfo: any) => void) => {
        if (timeoutId) {
          clearTimeout(timeoutId)
        }

        timeoutId = setTimeout(async () => {
          if (!url || !url.trim()) {
            callback(null)
            return
          }

          try {
            const locationInfo = await extractLocationFromMapsUrl(url)
            callback(locationInfo)
          } catch (error) {
            console.error('Error extracting location:', error)
            callback(null)
          }
        }, 1000)
      }
    })(),
    []
  )

  // Auto-fill location from selected project
  useEffect(() => {
    if (selectedProject && useProjectLocation && !isEditing) {
      setValue('location', selectedProject.location || '')
      setValue('google_maps_link', selectedProject.google_maps_link || '')
    }
  }, [selectedProject, useProjectLocation, setValue, isEditing])

  // Extract location when Google Maps link changes
  useEffect(() => {
    if (watchGoogleMapsLink && watchGoogleMapsLink.trim() && !useProjectLocation) {
      setExtractingLocation(true)
      debouncedExtractLocation(watchGoogleMapsLink, (locationInfo) => {
        setExtractingLocation(false)
        if (locationInfo && locationInfo.formattedLocation) {
          setValue('location', locationInfo.formattedLocation)
          toast.success(`Location extracted: ${locationInfo.formattedLocation}`)
        } else {
          // Check if it's a shortened URL
          const isShortenedUrl = watchGoogleMapsLink.includes('goo.gl') || watchGoogleMapsLink.includes('maps.app.goo.gl')
          if (isShortenedUrl) {
            toast.error('Unable to extract location from shortened URL. Please wait while we attempt to resolve it, or enter location manually.')
          } else {
            toast.error('Could not extract location from the provided link. Please check the URL format or enter location manually.')
          }
        }
      })
    } else {
      setExtractingLocation(false)
    }
  }, [watchGoogleMapsLink, debouncedExtractLocation, setValue, setExtractingLocation, useProjectLocation])

  // Auto-suggest end time when start time is set (add 2 hours by default)
  useEffect(() => {
    if (watchScheduledDate && !watchScheduledEndDate && !isEditing) {
      const startDate = new Date(watchScheduledDate)
      const endDate = new Date(startDate.getTime() + 2 * 60 * 60 * 1000) // Add 2 hours
      setValue('scheduled_end_date', endDate.toISOString().slice(0, 16))
    }
  }, [watchScheduledDate, watchScheduledEndDate, setValue, isEditing])

  const onSubmit = async (data: ShootFormData) => {
    try {
      // Convert the datetime-local input to ISO string
      const scheduledDate = new Date(data.scheduled_date).toISOString()
      
      const scheduledEndDate = data.scheduled_end_date ? new Date(data.scheduled_end_date).toISOString() : null

      let cleanData: any = {
        ...data,
        scheduled_date: scheduledDate,
        scheduled_end_date: scheduledEndDate,
        pilot_id: data.pilot_id || null,
        amount: data.amount || 0,
        location: data.location || null,
        google_maps_link: data.google_maps_link || null,
        notes: data.notes || null,
        recurring_pattern: data.is_recurring ? data.recurring_pattern : null,
        is_outsourced: data.is_outsourced || false,
        vendor_id: data.is_outsourced ? data.vendor_id : null,
        outsourcing_cost: data.is_outsourced ? data.outsourcing_cost : null,
      }

      let result: Shoot
      if (isEditing) {
        // Check if the scheduled date has changed to flag as rescheduled
        const originalDate = new Date(shoot.scheduled_date).getTime()
        const newDate = new Date(scheduledDate).getTime()
        const dateChanged = originalDate !== newDate

        // If date changed and shoot was scheduled, mark as rescheduled
        if (dateChanged && shoot.status === 'scheduled') {
          cleanData.status = 'rescheduled'
        }

        result = await updateShoot(shoot.id, cleanData)

        if (dateChanged && shoot.status === 'scheduled') {
          toast.success('Shoot rescheduled successfully')
        } else {
          toast.success('Shoot updated successfully')
        }
      } else {
        result = await createShoot(cleanData)
        toast.success('Shoot scheduled successfully')
        reset()
      }

      onSuccess?.(result)
    } catch (error: any) {
      toast.error(error.message || 'Failed to save shoot')
    }
  }

  if (projectsLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // Filter active projects
  const activeProjects = projects?.filter(p => p.status === 'active') || []

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Project Selection */}
      <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2 mb-3">
          <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <Label htmlFor="project_id" className="text-sm font-semibold text-gray-900 dark:text-gray-100">
            Project *
          </Label>
        </div>
        <select
          id="project_id"
          {...register('project_id')}
          className="w-full h-11 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-2 text-sm text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
        >
          <option value="">Choose a project</option>
          {activeProjects.map((project) => (
            <option key={project.id} value={project.id}>
              {project.name} - {project.client?.name}
            </option>
          ))}
        </select>
        {errors.project_id && (
          <p className="text-sm text-red-500 mt-2 flex items-center gap-1">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {errors.project_id.message}
          </p>
        )}
      </div>

      {/* Date & Time Section */}
      <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 className="text-sm font-semibold text-gray-900 dark:text-gray-100">Schedule</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="scheduled_date" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
              <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Start Time *
            </Label>
            <Input
              id="scheduled_date"
              type="datetime-local"
              {...register('scheduled_date')}
              className="w-full h-11 rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
            {errors.scheduled_date && (
              <p className="text-sm text-red-500 mt-1 flex items-center gap-1">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors.scheduled_date.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="scheduled_end_date" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center gap-2">
              <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              End Time
              <span className="text-xs text-gray-500 bg-gray-200 dark:bg-gray-700 px-2 py-0.5 rounded-full">optional</span>
            </Label>
            <Input
              id="scheduled_end_date"
              type="datetime-local"
              {...register('scheduled_end_date')}
              className="w-full h-11 rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
            {errors.scheduled_end_date && (
              <p className="text-sm text-red-500 mt-1 flex items-center gap-1">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors.scheduled_end_date.message}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Amount Section */}
      <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2 mb-3">
          <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
          <Label htmlFor="amount" className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            Amount
            <span className="text-xs text-gray-500 bg-gray-200 dark:bg-gray-700 px-2 py-0.5 rounded-full">optional</span>
          </Label>
        </div>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className="text-gray-500 text-sm">₹</span>
          </div>
          <Input
            id="amount"
            type="number"
            step="0.01"
            min="0"
            {...register('amount', { valueAsNumber: true })}
            placeholder="Enter shoot cost/budget"
            className="pl-8 h-11 rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
          />
        </div>
        {errors.amount && (
          <p className="text-sm text-red-500 mt-2 flex items-center gap-1">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {errors.amount.message}
          </p>
        )}
      </div>

      {/* Location Section */}
      <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <Label htmlFor="location" className="text-sm font-semibold text-gray-900 dark:text-gray-100">Location</Label>
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="useProjectLocation"
              checked={useProjectLocation}
              onChange={(e) => {
                setUseProjectLocation(e.target.checked)
                if (e.target.checked && selectedProject) {
                  setValue('location', selectedProject.location || '')
                  setValue('google_maps_link', selectedProject.google_maps_link || '')
                } else if (!e.target.checked) {
                  setValue('location', '')
                  setValue('google_maps_link', '')
                }
              }}
              className="w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
            />
            <Label htmlFor="useProjectLocation" className="text-sm text-gray-600 dark:text-gray-400">
              Use project location
            </Label>
          </div>
        </div>

        <div className="space-y-3">
          <Input
            id="location"
            {...register('location')}
            placeholder={useProjectLocation ? "Using project location" : "Enter location"}
            disabled={useProjectLocation}
            className="h-11 rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-red-500 focus:border-transparent disabled:bg-gray-100 dark:disabled:bg-gray-800"
          />
          {errors.location && (
            <p className="text-sm text-red-500 flex items-center gap-1">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              {errors.location.message}
            </p>
          )}

          {!useProjectLocation && (
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <Input
                id="google_maps_link"
                {...register('google_maps_link')}
                placeholder="Or paste Google Maps link for auto-extraction"
                className="pl-10 h-11 rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-red-500 focus:border-transparent text-sm"
              />
            </div>
          )}
        </div>

        {extractingLocation && (
          <p className="text-xs text-blue-600 dark:text-blue-400 flex items-center">
            <div className="animate-spin rounded-full h-3 w-3 border-b border-blue-600 mr-2"></div>
            Extracting location...
          </p>
        )}

        {selectedProject && useProjectLocation && selectedProject.location && (
          <p className="text-xs text-muted-foreground">
            📍 {selectedProject.location}
            {selectedProject.google_maps_link && (
              <span> • <a
                href={selectedProject.google_maps_link}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 dark:text-blue-400 hover:underline"
              >
                View on Maps
              </a></span>
            )}
          </p>
        )}
      </div>

      {/* Pilot Section */}
      <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2 mb-3">
          <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <Label htmlFor="pilot_id" className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            Pilot
            <span className="text-xs text-gray-500 bg-gray-200 dark:bg-gray-700 px-2 py-0.5 rounded-full">optional</span>
          </Label>
        </div>
        <select
          id="pilot_id"
          {...register('pilot_id')}
          className="w-full h-11 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-2 text-sm text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-purple-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={loadingUsers}
        >
          <option value="">Choose a pilot</option>
          {users.map((user) => (
            <option key={user.id} value={user.id}>
              {user.name} ({user.role}) - {user.email}
            </option>
          ))}
        </select>
        {loadingUsers && (
          <p className="text-xs text-gray-500 mt-2 flex items-center gap-1">
            <div className="animate-spin rounded-full h-3 w-3 border-b border-purple-600"></div>
            Loading pilots...
          </p>
        )}
        {errors.pilot_id && (
          <p className="text-sm text-red-500 mt-2 flex items-center gap-1">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {errors.pilot_id.message}
          </p>
        )}
      </div>

      {/* Notes Section */}
      <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2 mb-3">
          <div className="w-8 h-8 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </div>
          <Label htmlFor="notes" className="text-sm font-semibold text-gray-900 dark:text-gray-100 flex items-center gap-2">
            Notes
            <span className="text-xs text-gray-500 bg-gray-200 dark:bg-gray-700 px-2 py-0.5 rounded-full">optional</span>
          </Label>
        </div>
        <textarea
          id="notes"
          {...register('notes')}
          placeholder="Enter any notes about this shoot"
          className="w-full min-h-[80px] rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-3 text-sm text-gray-900 dark:text-gray-100 placeholder:text-gray-500 focus:ring-2 focus:ring-gray-500 focus:border-transparent resize-none"
          rows={3}
        />
        {errors.notes && (
          <p className="text-sm text-red-500 mt-2 flex items-center gap-1">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {errors.notes.message}
          </p>
        )}
      </div>

      {/* Outsourcing Section */}
      <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3 mb-4">
          <input
            id="is_outsourced"
            type="checkbox"
            {...register('is_outsourced')}
            className="w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2"
          />
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <Label htmlFor="is_outsourced" className="text-sm font-semibold text-gray-900 dark:text-gray-100">
              Outsourced Project
            </Label>
          </div>
        </div>

        {watchIsOutsourced && (
          <div className="space-y-4 mt-4 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div>
              <Label htmlFor="vendor_id" className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                Select Outsourcing Vendor
              </Label>
              <select
                id="vendor_id"
                {...register('vendor_id')}
                className="w-full h-11 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-2 text-sm text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={loadingVendors}
              >
                <option value="">Choose a vendor</option>
                {vendors.map((vendor) => (
                  <option key={vendor.id} value={vendor.id}>
                    {vendor.name} {vendor.specialization && `- ${vendor.specialization}`} {vendor.email && `(${vendor.email})`}
                  </option>
                ))}
              </select>
              {loadingVendors && (
                <p className="text-xs text-blue-600 mt-2 flex items-center gap-1">
                  <div className="animate-spin rounded-full h-3 w-3 border-b border-blue-600"></div>
                  Loading vendors...
                </p>
              )}
              {errors.vendor_id && (
                <p className="text-sm text-red-500 mt-2 flex items-center gap-1">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {errors.vendor_id.message}
                </p>
              )}
              <p className="text-xs text-blue-600 dark:text-blue-400 mt-2">
                Don't see your vendor? <Link href="/vendors" className="font-medium hover:underline">Add a new vendor</Link>
              </p>
            </div>

            <div>
              <Label htmlFor="outsourcing_cost" className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                Outsourcing Cost
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 text-sm">₹</span>
                </div>
                <Input
                  id="outsourcing_cost"
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('outsourcing_cost', { valueAsNumber: true })}
                  placeholder="Enter outsourcing cost"
                  className="pl-8 h-11 rounded-lg border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              {errors.outsourcing_cost && (
                <p className="text-sm text-red-500 mt-2 flex items-center gap-1">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {errors.outsourcing_cost.message}
                </p>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Recurring Section */}
      <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3 mb-4">
          <input
            id="is_recurring"
            type="checkbox"
            {...register('is_recurring')}
            className="w-4 h-4 rounded border-gray-300 text-green-600 focus:ring-green-500 focus:ring-2"
          />
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
              <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </div>
            <Label htmlFor="is_recurring" className="text-sm font-semibold text-gray-900 dark:text-gray-100">
              Recurring Shoot
            </Label>
          </div>
        </div>

        {watchIsRecurring && (
          <div className="mt-4">
            <Label htmlFor="recurring_pattern" className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              Recurring Pattern
            </Label>
            <select
              id="recurring_pattern"
              {...register('recurring_pattern')}
              className="w-full h-11 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-2 text-sm text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-green-500 focus:border-transparent"
            >
              <option value="">Choose pattern</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
            </select>
            {errors.recurring_pattern && (
              <p className="text-sm text-red-500 mt-2 flex items-center gap-1">
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
                {errors.recurring_pattern.message}
              </p>
            )}
          </div>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex gap-3 pt-6 border-t border-gray-200 dark:border-gray-700">
        <Button
          type="submit"
          disabled={loading}
          className="flex-1 h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              Saving...
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              {isEditing ? 'Update Shoot' : 'Schedule Shoot'}
            </div>
          )}
        </Button>
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
            className="px-6 h-12 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800 font-medium rounded-lg transition-colors duration-200"
          >
            Cancel
          </Button>
        )}
      </div>
    </form>
  )
}
