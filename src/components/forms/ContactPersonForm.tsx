'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { contactPersonsApi } from '@/lib/api'
import toast from 'react-hot-toast'
import { X, Star } from 'lucide-react'
import type { ContactPerson, CreateContactPersonForm } from '@/types'

const contactPersonSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  phone: z.string().optional(),
  email: z.string().email('Invalid email').optional().or(z.literal('')),
  designation: z.string().optional(),
  is_primary: z.boolean().optional(),
})

type ContactPersonFormData = z.infer<typeof contactPersonSchema>

interface ContactPersonFormProps {
  clientId: string
  contactPerson?: Contact<PERSON>erson
  onSuccess?: (contactPerson: ContactPerson) => void
  onCancel?: () => void
}

export function ContactPersonForm({ clientId, contactPerson, onSuccess, onCancel }: ContactPersonFormProps) {
  const isEditing = !!contactPerson
  const [loading, setLoading] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ContactPersonFormData>({
    resolver: zodResolver(contactPersonSchema),
    defaultValues: contactPerson ? {
      name: contactPerson.name,
      phone: contactPerson.phone || '',
      email: contactPerson.email || '',
      designation: contactPerson.designation || '',
      is_primary: contactPerson.is_primary,
    } : {
      name: '',
      phone: '',
      email: '',
      designation: '',
      is_primary: false,
    },
  })

  const onSubmit = async (data: ContactPersonFormData) => {
    try {
      setLoading(true)
      
      // Convert empty strings to null for optional fields
      const cleanData = {
        ...data,
        phone: data.phone || null,
        email: data.email || null,
        designation: data.designation || null,
      }

      let result: ContactPerson
      if (isEditing) {
        result = await contactPersonsApi.update(contactPerson.id, cleanData)
        toast.success('Contact person updated successfully')
      } else {
        result = await contactPersonsApi.create({ ...cleanData, client_id: clientId })
        toast.success('Contact person added successfully')
        reset()
      }

      // If this contact is set as primary, update the primary status
      if (data.is_primary) {
        await contactPersonsApi.setPrimary(result.id, clientId)
      }

      onSuccess?.(result)
    } catch (error: any) {
      toast.error(error.message || 'Failed to save contact person')
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <Label htmlFor="name">Name *</Label>
        <Input
          id="name"
          {...register('name')}
          placeholder="Enter contact person name"
          className="mt-1"
        />
        {errors.name && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.name.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="phone">Phone</Label>
        <Input
          id="phone"
          {...register('phone')}
          placeholder="Enter phone number"
          className="mt-1"
        />
        {errors.phone && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.phone.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          {...register('email')}
          placeholder="Enter email address"
          className="mt-1"
        />
        {errors.email && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.email.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="designation">Designation</Label>
        <Input
          id="designation"
          {...register('designation')}
          placeholder="Enter job title or designation"
          className="mt-1"
        />
        {errors.designation && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.designation.message}</p>
        )}
      </div>

      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="is_primary"
          {...register('is_primary')}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <Label htmlFor="is_primary" className="flex items-center space-x-1">
          <Star className="w-4 h-4" />
          <span>Primary Contact</span>
        </Label>
      </div>

      <div className="flex gap-3 pt-4">
        <Button
          type="submit"
          disabled={loading}
          className="flex-1"
        >
          {loading ? 'Saving...' : isEditing ? 'Update Contact' : 'Add Contact'}
        </Button>
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
        )}
      </div>
    </form>
  )
}
