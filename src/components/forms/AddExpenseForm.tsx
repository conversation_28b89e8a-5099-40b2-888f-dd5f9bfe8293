'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useCreateExpense } from '@/hooks/useApi'
import { useAuth } from '@/contexts/AuthContext'
import toast from 'react-hot-toast'
import type { Expense } from '@/types'

const expenseSchema = z.object({
  description: z.string().min(1, 'Description is required'),
  amount: z.string().min(1, 'Amount is required').refine(
    (val) => !isNaN(Number(val)) && Number(val) > 0,
    'Amount must be a positive number'
  ),
  category: z.enum(['fuel', 'equipment', 'travel', 'maintenance', 'other']),
  date: z.string().min(1, 'Date is required'),
  project_id: z.string().optional(),
  receipt_url: z.string().optional(),
})

type ExpenseFormData = z.infer<typeof expenseSchema>

interface AddExpenseFormProps {
  onSuccess?: (expense: Expense) => void
  projectId?: string
  onCancel?: () => void
}

export function AddExpenseForm({ onSuccess, projectId }: AddExpenseFormProps) {
  const { user } = useAuth()
  const { createExpense, loading } = useCreateExpense()

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ExpenseFormData>({
    resolver: zodResolver(expenseSchema),
    defaultValues: {
      description: '',
      amount: '',
      category: 'fuel',
      date: new Date().toISOString().slice(0, 10),
      project_id: projectId || '',
      receipt_url: '',
    },
  })

  const onSubmit = async (data: ExpenseFormData) => {
    try {
      if (!user) {
        toast.error('You must be logged in to record expenses')
        return
      }

      const expenseData = {
        ...data,
        amount: parseFloat(data.amount),
        project_id: data.project_id || null,
        receipt_url: data.receipt_url || null,
        user_id: user.id,
      }

      const result = await createExpense(expenseData)
      toast.success('Expense recorded successfully')
      reset()

      onSuccess?.(result)
    } catch (error: any) {
      toast.error(error.message || 'Failed to save expense')
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <Label htmlFor="description">Description *</Label>
        <Input
          id="description"
          {...register('description')}
          placeholder="Enter expense description"
          className="mt-1"
        />
        {errors.description && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.description.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="amount">Amount (₹) *</Label>
        <Input
          id="amount"
          type="number"
          step="0.01"
          min="0"
          {...register('amount')}
          placeholder="Enter expense amount"
          className="mt-1"
        />
        {errors.amount && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.amount.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="category">Category *</Label>
        <select
          id="category"
          {...register('category')}
          className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        >
          <option value="fuel">Fuel</option>
          <option value="equipment">Equipment</option>
          <option value="travel">Travel</option>
          <option value="maintenance">Maintenance</option>
          <option value="other">Other</option>
        </select>
        {errors.category && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.category.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="date">Date *</Label>
        <Input
          id="date"
          type="date"
          {...register('date')}
          className="mt-1"
        />
        {errors.date && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.date.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="receipt_url">Receipt URL</Label>
        <Input
          id="receipt_url"
          type="url"
          {...register('receipt_url')}
          placeholder="https://example.com/receipt.jpg"
          className="mt-1"
        />
        <p className="text-xs text-muted-foreground mt-1">
          Upload receipt to cloud storage and paste the URL here
        </p>
        {errors.receipt_url && (
          <p className="text-sm text-red-600 dark:text-red-400 mt-1">{errors.receipt_url.message}</p>
        )}
      </div>

      <div className="flex gap-3 pt-4">
        <Button
          type="submit"
          disabled={loading}
          className="flex-1"
        >
          {loading ? 'Recording...' : 'Record Expense'}
        </Button>
      </div>
    </form>
  )
}