'use client'

import { useEffect, useCallback, useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ClientSelector } from '@/components/ui/client-selector'
import { ContactPersonSelector } from '@/components/ui/contact-person-selector'
import { useCreateProject, useUpdateProject } from '@/hooks/useApi'
import { createDebouncedLocationExtractor } from '@/lib/maps-utils'
import toast from 'react-hot-toast'
import type { Project } from '@/types'

const projectSchema = z.object({
  name: z.string().min(1, 'Project name is required'),
  description: z.string().optional(),
  client_id: z.string().min(1, 'Client is required'),
  contact_person_id: z.string().optional(),
  location: z.string().optional(),
  google_maps_link: z.string().optional().refine((val) => {
    if (!val || val.trim() === '') return true
    try {
      new URL(val)
      return true
    } catch {
      return false
    }
  }, {
    message: 'Invalid URL'
  }),
})

type ProjectFormData = z.infer<typeof projectSchema>

interface ProjectFormProps {
  project?: Project
  onSuccess?: (project: Project) => void
  onCancel?: () => void
  preselectedClientId?: string
}

export function ProjectForm({ project, onSuccess, onCancel, preselectedClientId }: ProjectFormProps) {
  const isEditing = !!project
  const { createProject, loading: createLoading } = useCreateProject()
  const { updateProject, loading: updateLoading } = useUpdateProject()
  const loading = createLoading || updateLoading
  const [extractingLocation, setExtractingLocation] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
    defaultValues: project ? {
      name: project.name,
      description: project.description || '',
      client_id: project.client_id,
      contact_person_id: project.contact_person_id || '',
      location: project.location || '',
      google_maps_link: project.google_maps_link || '',
    } : {
      name: '',
      description: '',
      client_id: preselectedClientId || '',
      contact_person_id: '',
      location: '',
      google_maps_link: '',
    },
  })

  // Watch for changes in Google Maps link
  const googleMapsLink = watch('google_maps_link')

  // Create debounced location extractor
  const debouncedExtractLocation = useCallback(
    createDebouncedLocationExtractor(1500),
    []
  )

  // Extract location when Google Maps link changes
  useEffect(() => {
    if (googleMapsLink && googleMapsLink.trim()) {
      setExtractingLocation(true)
      debouncedExtractLocation(googleMapsLink, (locationInfo) => {
        setExtractingLocation(false)
        if (locationInfo && locationInfo.formattedLocation) {
          setValue('location', locationInfo.formattedLocation)
          toast.success(`Location extracted: ${locationInfo.formattedLocation}`)
        } else {
          // Check if it's a shortened URL
          const isShortenedUrl = googleMapsLink.includes('goo.gl') || googleMapsLink.includes('maps.app.goo.gl')
          if (isShortenedUrl) {
            toast.error('Unable to extract location from shortened URL. Please wait while we attempt to resolve it, or enter location manually.')
          } else {
            toast.error('Could not extract location from the provided link. Please check the URL format or enter location manually.')
          }
        }
      })
    } else {
      setExtractingLocation(false)
    }
  }, [googleMapsLink, debouncedExtractLocation, setValue, setExtractingLocation])

  const onSubmit = async (data: ProjectFormData) => {
    try {
      // Convert empty strings to null for optional fields
      const cleanData = {
        ...data,
        description: data.description || null,
        contact_person_id: data.contact_person_id || null,
        location: data.location || null,
        google_maps_link: data.google_maps_link || null,
        total_amount: 0, // Will be calculated from shoots
        gst_inclusive: false, // Will be determined by client GST status
        amount_received: 0,
        amount_pending: 0,
      }

      let result: Project
      if (isEditing) {
        result = await updateProject(project.id, cleanData)
        toast.success('Project updated successfully')
      } else {
        result = await createProject(cleanData)
        toast.success('Project created successfully')
        reset()
      }

      onSuccess?.(result)
    } catch (error: any) {
      toast.error(error.message || 'Failed to save project')
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <Label htmlFor="name">Project Name *</Label>
        <Input
          id="name"
          {...register('name')}
          placeholder="Enter project name"
          className="mt-1"
        />
        {errors.name && (
          <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
        )}
      </div>

      <ClientSelector
        value={watch('client_id')}
        onChange={(clientId) => {
          setValue('client_id', clientId)
          setValue('contact_person_id', '') // Reset contact person when client changes
        }}
        error={errors.client_id?.message}
        disabled={!!preselectedClientId}
        label="Client"
        required
      />

      <ContactPersonSelector
        clientId={watch('client_id')}
        value={watch('contact_person_id')}
        onChange={(contactPersonId) => setValue('contact_person_id', contactPersonId)}
        error={errors.contact_person_id?.message}
        label="Primary Contact"
      />

      <div>
        <Label htmlFor="description">Description</Label>
        <textarea
          id="description"
          {...register('description')}
          placeholder="Enter project description"
          className="mt-1 flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          rows={3}
        />
        {errors.description && (
          <p className="text-sm text-red-600 mt-1">{errors.description.message}</p>
        )}
      </div>

      <div>
        <Label htmlFor="location">Location</Label>
        <div className="relative">
          <Input
            id="location"
            {...register('location')}
            placeholder="Enter project location (or paste Google Maps link below)"
            className="mt-1"
          />
          {extractingLocation && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 mt-1">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            </div>
          )}
        </div>
        {errors.location && (
          <p className="text-sm text-red-600 mt-1">{errors.location.message}</p>
        )}
        {extractingLocation && (
          <p className="text-sm text-blue-600 mt-1">Extracting location from Google Maps link...</p>
        )}
      </div>

      <div>
        <Label htmlFor="google_maps_link">Google Maps Link</Label>
        <Input
          id="google_maps_link"
          {...register('google_maps_link')}
          placeholder="https://maps.google.com/... (location will be auto-extracted)"
          className="mt-1"
        />
        {errors.google_maps_link && (
          <p className="text-sm text-red-600 mt-1">{errors.google_maps_link.message}</p>
        )}
        <p className="text-xs text-gray-500 mt-1">
          Paste a Google Maps link and the location will be automatically extracted above.
          <br />
          <span className="text-xs">💡 For best results, use the full URL from your browser's address bar instead of shortened links.</span>
        </p>
      </div>



      <div className="flex gap-3 pt-4">
        <Button
          type="submit"
          disabled={loading}
          className="flex-1"
        >
          {loading ? 'Saving...' : isEditing ? 'Update Project' : 'Create Project'}
        </Button>
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
        )}
      </div>
    </form>
  )
}
