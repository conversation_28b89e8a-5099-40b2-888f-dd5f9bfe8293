'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { formatDate } from '@/lib/utils'
import { 
  CreditCard, 
  Banknote, 
  Building, 
  FileText, 
  Edit, 
  Trash2,
  CheckCircle,
  Hash
} from 'lucide-react'
import type { Payment } from '@/types'

interface PaymentCardProps {
  payment: Payment
  onEdit?: (payment: Payment) => void
  onDelete?: (payment: Payment) => void
  compact?: boolean
}

export function PaymentCard({ payment, onEdit, onDelete, compact = false }: PaymentCardProps) {
  const [isDeleting, setIsDeleting] = useState(false)

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'cash':
        return <Banknote className="w-4 h-4" />
      case 'bank_transfer':
        return <Building className="w-4 h-4" />
      case 'credit_card':
        return <CreditCard className="w-4 h-4" />
      case 'cheque':
        return <FileText className="w-4 h-4" />
      default:
        return <CheckCircle className="w-4 h-4" />
    }
  }

  const getPaymentMethodLabel = (method: string) => {
    switch (method) {
      case 'cash':
        return 'Cash'
      case 'bank_transfer':
        return 'Bank Transfer'
      case 'credit_card':
        return 'Credit Card'
      case 'cheque':
        return 'Cheque'
      case 'other':
        return 'Other'
      default:
        return method
    }
  }

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete this payment of ₹${payment.amount.toLocaleString()}? This action cannot be undone.`)) {
      return
    }

    setIsDeleting(true)
    try {
      await onDelete?.(payment)
    } finally {
      setIsDeleting(false)
    }
  }

  if (compact) {
    return (
      <div className="bg-card rounded-lg border border-border p-4 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3">
              <div className="flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-500/10 text-green-500 border border-green-500/20">
                {getPaymentMethodIcon(payment.payment_method)}
                <span className="ml-1">{getPaymentMethodLabel(payment.payment_method)}</span>
              </div>
              <div>
                <h4 className="font-medium text-foreground">₹{payment.amount.toLocaleString()}</h4>
                <p className="text-sm text-muted-foreground">{payment.project?.name}</p>
              </div>
            </div>
            <div className="mt-2 flex items-center space-x-4 text-sm text-muted-foreground">
              <span>{formatDate(payment.payment_date)}</span>
              {payment.reference_number && (
                <div className="flex items-center">
                  <Hash className="w-3 h-3 mr-1" />
                  {payment.reference_number}
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {onEdit && (
              <Button variant="outline" size="sm" onClick={() => onEdit(payment)}>
                <Edit className="w-4 h-4" />
              </Button>
            )}
            {onDelete && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDelete}
                disabled={isDeleting}
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-card rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden border border-border">
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <div className="flex items-center space-x-3 mb-2">
              <div className="text-2xl font-bold text-green-600">
                ₹{payment.amount.toLocaleString()}
              </div>
              <div className="flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-500/10 text-green-500 border border-green-500/20">
                {getPaymentMethodIcon(payment.payment_method)}
                <span className="ml-1">{getPaymentMethodLabel(payment.payment_method)}</span>
              </div>
            </div>
            <h3 className="text-lg font-semibold text-card-foreground mb-1">
              {payment.project?.name}
            </h3>
            <div className="flex items-center text-sm text-muted-foreground">
              <Building className="w-4 h-4 mr-1" />
              {payment.project?.client?.name}
            </div>
          </div>
        </div>

        {/* Payment Details */}
        <div className="space-y-2 mb-4 text-sm text-muted-foreground">
          <div className="flex items-center">
            <span className="font-medium">Date:</span>
            <span className="ml-2">{formatDate(payment.payment_date)}</span>
          </div>

          {payment.reference_number && (
            <div className="flex items-center">
              <Hash className="w-4 h-4 mr-1" />
              <span className="font-medium">Reference:</span>
              <span className="ml-2">{payment.reference_number}</span>
            </div>
          )}
        </div>

        {/* Notes */}
        {payment.notes && (
          <div className="text-sm text-muted-foreground mb-3">
            <div className="flex items-start">
              <FileText className="w-4 h-4 mr-2 mt-0.5" />
              <p className="line-clamp-2">{payment.notes}</p>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-4 bg-muted/50 border-t border-border">
        <div className="flex items-center justify-between">
          <div className="text-xs text-muted-foreground">
            Recorded {formatDate(payment.created_at)}
          </div>
          
          <div className="flex items-center space-x-2">
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(payment)}
                className="text-xs"
              >
                <Edit className="w-3 h-3" />
              </Button>
            )}
            {onDelete && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDelete}
                disabled={isDeleting}
                className="text-xs"
              >
                <Trash2 className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
