'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Modal } from '@/components/ui/modal'
import { CheckCircle, Clock, Battery, Camera } from 'lucide-react'
import type { Shoot } from '@/types'

interface ShootCompletionFormProps {
  shoot: Shoot | null
  isOpen: boolean
  onClose: () => void
  onComplete: (completionData: ShootCompletionData) => void
}

export interface ShootCompletionData {
  device_used: string
  battery_count: number
  shoot_start_time: string
  shoot_end_time: string
  completion_notes?: string
}

export function ShootCompletionForm({ shoot, isOpen, onClose, onComplete }: ShootCompletionFormProps) {
  const [formData, setFormData] = useState<ShootCompletionData>({
    device_used: '',
    battery_count: 1,
    shoot_start_time: '',
    shoot_end_time: '',
    completion_notes: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.device_used || !formData.shoot_start_time || !formData.shoot_end_time) {
      return
    }

    // Validate that end time is after start time
    if (formData.shoot_end_time <= formData.shoot_start_time) {
      alert('End time must be after start time')
      return
    }

    setIsSubmitting(true)
    try {
      await onComplete(formData)
      onClose()
      // Reset form
      setFormData({
        device_used: '',
        battery_count: 1,
        shoot_start_time: '',
        shoot_end_time: '',
        completion_notes: ''
      })
    } catch (error) {
      console.error('Error completing shoot:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (field: keyof ShootCompletionData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  if (!shoot) return null

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Complete Shoot">
      <div className="space-y-6">
        {/* Shoot Info */}
        <div className="bg-muted p-4 rounded-lg">
          <h3 className="font-medium text-sm text-muted-foreground mb-2">Completing Shoot</h3>
          <p className="font-medium">{new Date(shoot.scheduled_date).toLocaleDateString()}</p>
          <p className="text-sm text-muted-foreground">{shoot.location}</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Device Used */}
          <div className="space-y-2">
            <Label htmlFor="device_used" className="flex items-center gap-2">
              <Camera className="w-4 h-4" />
              Device Used *
            </Label>
            <Input
              id="device_used"
              value={formData.device_used}
              onChange={(e) => handleInputChange('device_used', e.target.value)}
              placeholder="e.g., Canon EOS R5, DJI Mavic 3"
              required
            />
          </div>

          {/* Battery Count */}
          <div className="space-y-2">
            <Label htmlFor="battery_count" className="flex items-center gap-2">
              <Battery className="w-4 h-4" />
              Number of Batteries Used *
            </Label>
            <Input
              id="battery_count"
              type="number"
              min="1"
              value={formData.battery_count}
              onChange={(e) => handleInputChange('battery_count', parseInt(e.target.value) || 1)}
              required
            />
          </div>

          {/* Timing */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="shoot_start_time" className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Start Time *
              </Label>
              <Input
                id="shoot_start_time"
                type="datetime-local"
                value={formData.shoot_start_time}
                onChange={(e) => handleInputChange('shoot_start_time', e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="shoot_end_time" className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                End Time *
              </Label>
              <Input
                id="shoot_end_time"
                type="datetime-local"
                value={formData.shoot_end_time}
                onChange={(e) => handleInputChange('shoot_end_time', e.target.value)}
                required
              />
            </div>
          </div>

          {/* Completion Notes */}
          <div className="space-y-2">
            <Label htmlFor="completion_notes">Additional Notes</Label>
            <Textarea
              id="completion_notes"
              value={formData.completion_notes}
              onChange={(e) => handleInputChange('completion_notes', e.target.value)}
              placeholder="Any additional notes about the shoot completion..."
              rows={3}
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.device_used || !formData.shoot_start_time || !formData.shoot_end_time}
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b border-white mr-2"></div>
                  Completing...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Complete Shoot
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  )
}
