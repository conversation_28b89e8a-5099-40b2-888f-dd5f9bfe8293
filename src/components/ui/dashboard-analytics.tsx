'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { 
  <PERSON><PERSON>hart<PERSON>, 
  <PERSON><PERSON><PERSON>, 
  TrendingUp, 
  TrendingDown,
  Calendar,
  MapPin,
  Users,
  DollarSign,
  Target,
  Activity,
  Clock,
  CheckCircle
} from 'lucide-react'

interface AnalyticsData {
  revenue: {
    total: number
    monthly: number
    growth: number
  }
  projects: {
    total: number
    active: number
    completed: number
    completionRate: number
  }
  clients: {
    total: number
    active: number
    retention: number
  }
  performance: {
    avgProjectValue: number
    avgCompletionTime: number
    efficiency: number
  }
}

interface DashboardAnalyticsProps {
  data: AnalyticsData
  timeRange: 'week' | 'month' | 'quarter'
  onTimeRangeChange: (range: 'week' | 'month' | 'quarter') => void
}

export function DashboardAnalytics({ data, timeRange, onTimeRangeChange }: DashboardAnalyticsProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'revenue' | 'projects' | 'performance'>('overview')

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-600 dark:text-green-400'
    if (growth < 0) return 'text-red-600 dark:text-red-400'
    return 'text-muted-foreground'
  }

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return <TrendingUp className="w-4 h-4" />
    if (growth < 0) return <TrendingDown className="w-4 h-4" />
    return <Activity className="w-4 h-4" />
  }

  return (
    <div className="bg-card rounded-lg shadow border border-border">
      {/* Header */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <BarChart3 className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-foreground">Business Analytics</h3>
              <p className="text-sm text-muted-foreground">Comprehensive insights into your drone service business</p>
            </div>
          </div>

          {/* Time Range Selector */}
          <div className="flex space-x-1 bg-muted rounded-lg p-1">
            {[ 
              { id: 'week', label: 'Week' },
              { id: 'month', label: 'Month' },
              { id: 'quarter', label: 'Quarter' }
            ].map(range => (
              <button
                key={range.id}
                onClick={() => onTimeRangeChange(range.id as any)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  timeRange === range.id
                    ? 'bg-background text-foreground shadow'
                    : 'text-muted-foreground hover:text-foreground'
                }`}
              >
                {range.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-border">
        <div className="flex space-x-8 px-6">
          {[
            { id: 'overview', label: 'Overview', icon: Activity },
            { id: 'revenue', label: 'Revenue', icon: DollarSign },
            { id: 'projects', label: 'Projects', icon: Target },
            { id: 'performance', label: 'Performance', icon: TrendingUp }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center space-x-2 py-4 border-b-2 transition-colors ${
                activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span className="font-medium">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-primary/10 border border-primary/20 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <DollarSign className="w-8 h-8 text-primary" />
                <div className={`flex items-center space-x-1 ${getGrowthColor(data.revenue.growth)}`}>
                  {getGrowthIcon(data.revenue.growth)}
                  <span className="text-sm font-medium">{Math.abs(data.revenue.growth)}%</span>
                </div>
              </div>
              <div className="text-2xl font-bold text-foreground">₹{data.revenue.total.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground">Total Revenue</div>
            </div>

            <div className="bg-green-600/10 border border-green-600/20 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <Target className="w-8 h-8 text-green-600" />
                <div className="text-green-600">
                  <CheckCircle className="w-5 h-5" />
                </div>
              </div>
              <div className="text-2xl font-bold text-foreground">{data.projects.completed}</div>
              <div className="text-sm text-muted-foreground">Completed Projects</div>
            </div>

            <div className="bg-purple-600/10 border border-purple-600/20 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <Users className="w-8 h-8 text-purple-600" />
                <div className="text-purple-600">
                  <span className="text-sm font-medium">{data.clients.retention}%</span>
                </div>
              </div>
              <div className="text-2xl font-bold text-foreground">{data.clients.active}</div>
              <div className="text-sm text-muted-foreground">Active Clients</div>
            </div>

            <div className="bg-orange-600/10 border border-orange-600/20 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <Clock className="w-8 h-8 text-orange-600" />
                <div className="text-orange-600">
                  <span className="text-sm font-medium">{data.performance.efficiency}%</span>
                </div>
              </div>
              <div className="text-2xl font-bold text-foreground">{data.performance.avgCompletionTime}</div>
              <div className="text-sm text-muted-foreground">Avg. Days to Complete</div>
            </div>
          </div>
        )}

        {activeTab === 'revenue' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-green-600/10 border border-green-600/20 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <TrendingUp className="w-5 h-5 text-green-600" />
                  <span className="font-medium text-foreground">Monthly Revenue</span>
                </div>
                <div className="text-xl font-bold text-foreground">₹{data.revenue.monthly.toLocaleString()}</div>
                <div className={`text-sm flex items-center space-x-1 ${getGrowthColor(data.revenue.growth)}`}>
                  {getGrowthIcon(data.revenue.growth)}
                  <span>{Math.abs(data.revenue.growth)}% vs last {timeRange}</span>
                </div>
              </div>

              <div className="bg-primary/10 border border-primary/20 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Target className="w-5 h-5 text-primary" />
                  <span className="font-medium text-foreground">Avg. Project Value</span>
                </div>
                <div className="text-xl font-bold text-foreground">₹{data.performance.avgProjectValue.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Per project average</div>
              </div>

              <div className="bg-purple-600/10 border border-purple-600/20 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <PieChart className="w-5 h-5 text-purple-600" />
                  <span className="font-medium text-foreground">Revenue Sources</span>
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Real Estate</span>
                    <span className="font-medium text-foreground">65%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Events</span>
                    <span className="font-medium text-foreground">25%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">Commercial</span>
                    <span className="font-medium text-foreground">10%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Revenue Chart Placeholder */}
            <div className="bg-muted/50 border border-border rounded-lg p-6 text-center">
              <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h4 className="text-lg font-medium text-foreground mb-2">Revenue Trends</h4>
              <p className="text-muted-foreground">Interactive revenue chart would be displayed here</p>
            </div>
          </div>
        )}

        {activeTab === 'projects' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-card border border-border rounded-lg p-4">
                <h4 className="font-medium text-foreground mb-4">Project Status Distribution</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-muted-foreground">Completed</span>
                    </div>
                    <span className="font-medium text-foreground">{data.projects.completed}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                      <span className="text-sm text-muted-foreground">Active</span>
                    </div>
                    <span className="font-medium text-foreground">{data.projects.active}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
                      <span className="text-sm text-muted-foreground">Total</span>
                    </div>
                    <span className="font-medium text-foreground">{data.projects.total}</span>
                  </div>
                </div>
              </div>

              <div className="bg-card border border-border rounded-lg p-4">
                <h4 className="font-medium text-foreground mb-4">Performance Metrics</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Completion Rate</span>
                    <span className="font-medium text-green-600">{data.projects.completionRate}%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Avg. Completion Time</span>
                    <span className="font-medium text-foreground">{data.performance.avgCompletionTime} days</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Efficiency Score</span>
                    <span className="font-medium text-primary">{data.performance.efficiency}%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Project Timeline Placeholder */}
            <div className="bg-muted/50 border border-border rounded-lg p-6 text-center">
              <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h4 className="text-lg font-medium text-foreground mb-2">Project Timeline</h4>
              <p className="text-muted-foreground">Project timeline and milestones would be displayed here</p>
            </div>
          </div>
        )}

        {activeTab === 'performance' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center p-4 border border-border rounded-lg bg-card">
                <div className="text-2xl font-bold text-foreground">{data.performance.efficiency}%</div>
                <div className="text-sm text-muted-foreground">Overall Efficiency</div>
              </div>
              <div className="text-center p-4 border border-border rounded-lg bg-card">
                <div className="text-2xl font-bold text-foreground">{data.clients.retention}%</div>
                <div className="text-sm text-muted-foreground">Client Retention</div>
              </div>
              <div className="text-center p-4 border border-border rounded-lg bg-card">
                <div className="text-2xl font-bold text-foreground">{data.performance.avgCompletionTime}</div>
                <div className="text-sm text-muted-foreground">Avg. Days to Complete</div>
              </div>
              <div className="text-center p-4 border border-border rounded-lg bg-card">
                <div className="text-2xl font-bold text-foreground">₹{data.performance.avgProjectValue.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Avg. Project Value</div>
              </div>
            </div>

            {/* Performance Insights */}
            <div className="bg-primary/10 border border-primary/20 rounded-lg p-4">
              <h4 className="font-medium text-primary mb-3">Performance Insights</h4>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-primary" />
                  <span>Project completion rate is above industry average</span>
                </div>
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-4 h-4 text-primary" />
                  <span>Revenue growth trending upward this {timeRange}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="w-4 h-4 text-primary" />
                  <span>Client retention rate indicates strong satisfaction</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
