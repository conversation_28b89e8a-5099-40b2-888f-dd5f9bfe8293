'use client'

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Modal } from '@/components/ui/modal'
import { ContactPersonForm } from '@/components/forms/ContactPersonForm'
import { contactPersonsApi } from '@/lib/api'
import { User, ChevronDown, Check, Plus, Star, Phone, Mail, Briefcase } from 'lucide-react'
import toast from 'react-hot-toast'
import type { ContactPerson } from '@/types'

interface ContactPersonSelectorProps {
  clientId: string
  value?: string
  onChange: (contactPersonId: string) => void
  error?: string
  disabled?: boolean
  label?: string
  required?: boolean
}

export function ContactPersonSelector({ 
  clientId,
  value, 
  onChange, 
  error, 
  disabled = false, 
  label = "Primary Contact",
  required = false 
}: ContactPersonSelectorProps) {
  const [contactPersons, setContactPersons] = useState<ContactPerson[]>([])
  const [loading, setLoading] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const selectedContact = contactPersons.find(contact => contact.id === value)
  
  useEffect(() => {
    if (clientId) {
      fetchContactPersons()
    }
  }, [clientId])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const fetchContactPersons = async () => {
    try {
      setLoading(true)
      const data = await contactPersonsApi.getByClientId(clientId)
      setContactPersons(data)

      // Auto-select primary contact if no contact is selected
      if (!value && data.length > 0) {
        const primaryContact = data.find(contact => contact.is_primary) || data[0]
        onChange(primaryContact.id)
      }
    } catch (error: any) {
      console.error('Error fetching contact persons:', error)
      toast.error('Failed to load contact persons')
    } finally {
      setLoading(false)
    }
  }

  const handleContactSelect = (contact: ContactPerson) => {
    onChange(contact.id)
    setIsOpen(false)
  }

  const handleCreateSuccess = (newContact: ContactPerson) => {
    setIsCreateModalOpen(false)
    fetchContactPersons()
    onChange(newContact.id)
    setIsOpen(false)
  }

  const handleInputClick = () => {
    if (!disabled && contactPersons.length > 0) {
      setIsOpen(true)
    }
  }

  if (!clientId) {
    return (
      <div>
        <Label className="text-muted-foreground">{label} {required && '*'}</Label>
        <div className="mt-1 flex h-10 w-full rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground">
          Select a client first
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div>
        <Label>{label} {required && '*'}</Label>
        <div className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          <span className="ml-2 text-muted-foreground">Loading contacts...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <Label htmlFor="contact-selector">{label} {required && '*'}</Label>
      
      <div className="relative mt-1">
        <div
          className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 cursor-pointer ${
            disabled ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          onClick={handleInputClick}
        >
          <div className="flex-1 flex items-center min-w-0">
            {selectedContact ? (
              <div className="flex items-center space-x-2 min-w-0">
                <User className="w-4 h-4 text-muted-foreground flex-shrink-0" />
                <div className="min-w-0 flex-1">
                  <div className="flex items-center space-x-1">
                    <span className="font-medium truncate">{selectedContact.name}</span>
                    {selectedContact.is_primary && (
                      <Star className="w-3 h-3 text-yellow-500 flex-shrink-0" />
                    )}
                  </div>
                  {selectedContact.designation && (
                    <div className="text-xs text-muted-foreground truncate">{selectedContact.designation}</div>
                  )}
                </div>
              </div>
            ) : (
              <span className="text-muted-foreground">Select primary contact</span>
            )}
          </div>
          <ChevronDown className={`w-4 h-4 text-muted-foreground transition-transform flex-shrink-0 ${isOpen ? 'rotate-180' : ''}`} />
        </div>

        {isOpen && contactPersons.length > 0 && (
          <div className="absolute z-50 w-full mt-1 border border-border rounded-md shadow-lg max-h-60 overflow-auto"
               style={{ backgroundColor: '#000000' }}>
            <div className="py-1">
              {contactPersons.map((contact) => (
                <div
                  key={contact.id}
                  className="flex items-center justify-between px-3 py-2 text-sm cursor-pointer hover:bg-gray-800"
                  onClick={() => handleContactSelect(contact)}
                >
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-medium text-white truncate">{contact.name}</span>
                      {contact.is_primary && (
                        <Star className="w-3 h-3 text-yellow-400 flex-shrink-0" />
                      )}
                    </div>
                    
                    <div className="space-y-1">
                      {contact.designation && (
                        <div className="flex items-center space-x-1 text-xs text-gray-300">
                          <Briefcase className="w-3 h-3" />
                          <span className="truncate">{contact.designation}</span>
                        </div>
                      )}
                      {contact.phone && (
                        <div className="flex items-center space-x-1 text-xs text-gray-300">
                          <Phone className="w-3 h-3" />
                          <span className="truncate">{contact.phone}</span>
                        </div>
                      )}
                      {contact.email && (
                        <div className="flex items-center space-x-1 text-xs text-gray-300">
                          <Mail className="w-3 h-3" />
                          <span className="truncate">{contact.email}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  {value === contact.id && (
                    <Check className="w-4 h-4 text-blue-400 flex-shrink-0 ml-2" />
                  )}
                </div>
              ))}
            </div>
            
            <div className="border-t border-gray-700 px-3 py-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setIsCreateModalOpen(true)
                  setIsOpen(false)
                }}
                className="w-full justify-start text-white border-gray-600 hover:bg-gray-800"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add new contact person
              </Button>
            </div>
          </div>
        )}

        {isOpen && contactPersons.length === 0 && (
          <div className="absolute z-50 w-full mt-1 border border-border rounded-md shadow-lg"
               style={{ backgroundColor: '#000000' }}>
            <div className="px-3 py-2">
              <div className="text-sm text-gray-300 mb-2">No contact persons found</div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setIsCreateModalOpen(true)
                  setIsOpen(false)
                }}
                className="w-full justify-start text-white border-gray-600 hover:bg-gray-800"
              >
                <Plus className="w-4 h-4 mr-2" />
                Add first contact person
              </Button>
            </div>
          </div>
        )}
      </div>

      {error && (
        <p className="text-sm text-red-600 mt-1">{error}</p>
      )}

      {/* Create Contact Person Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Add Contact Person"
        size="lg"
      >
        <ContactPersonForm
          clientId={clientId}
          onSuccess={handleCreateSuccess}
          onCancel={() => setIsCreateModalOpen(false)}
        />
      </Modal>
    </div>
  )
}
