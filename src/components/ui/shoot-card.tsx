'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/dropdown-menu'
import { formatDate, formatDateTime } from '@/lib/utils'
import {
  Calendar,
  Clock,
  User,
  MapPin,
  Edit,
  CheckCircle,
  AlertCircle,
  XCircle,
  Building,
  FileText,
  MoreVertical,
  X
} from 'lucide-react'
import type { Shoot } from '@/types'

interface ShootCardProps {
  shoot: Shoot
  onEdit?: (shoot: Shoot) => void
  onStatusChange?: (shoot: Shoot, status: string) => void
  onCancel?: (shoot: Shoot) => void
  compact?: boolean
}

export function ShootCard({ shoot, onEdit, onStatusChange, onCancel, compact = false }: ShootCardProps) {

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border-blue-200 dark:border-blue-800'
      case 'completed':
        return 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 border-green-200 dark:border-green-800'
      case 'cancelled':
        return 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 border-red-200 dark:border-red-800'
      case 'rescheduled':
        return 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300 border-yellow-200 dark:border-yellow-800'
      default:
        return 'bg-muted text-muted-foreground border-border'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Clock className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'cancelled':
        return <XCircle className="w-4 h-4" />
      case 'rescheduled':
        return <RotateCcw className="w-4 h-4" />
      default:
        return <AlertCircle className="w-4 h-4" />
    }
  }

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete this shoot? This action cannot be undone.`)) {
      return
    }

    setIsDeleting(true)
    try {
      await onDelete?.(shoot)
    } finally {
      setIsDeleting(false)
    }
  }

  const isUpcoming = new Date(shoot.scheduled_date) > new Date()
  const isPast = new Date(shoot.scheduled_date) < new Date()

  if (compact) {
    return (
      <div className="bg-card rounded-lg border border-border p-4 hover:shadow-md transition-shadow">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-3 mb-2">
              <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(shoot.status)}`}>
                {getStatusIcon(shoot.status)}
                <span className="ml-1 capitalize">{shoot.status}</span>
              </div>
              <div className="min-w-0 flex-1">
                <h4 className="font-medium text-card-foreground truncate">{shoot.project?.name}</h4>
                <p className="text-sm text-muted-foreground truncate">{shoot.project?.client?.name}</p>
              </div>
            </div>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Calendar className="w-4 h-4 mr-1 flex-shrink-0" />
                <span className="truncate">{formatDateTime(shoot.scheduled_date)}</span>
              </div>
              {shoot.project?.location && (
                <div className="flex items-center min-w-0">
                  <MapPin className="w-4 h-4 mr-1 flex-shrink-0" />
                  <span className="truncate">{shoot.project.location}</span>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2 ml-4 flex-shrink-0">
            {/* Amount display */}
            {shoot.amount && (
              <div className="text-sm font-medium text-green-600 dark:text-green-400 mr-2">
                ₹{shoot.amount.toLocaleString()}
              </div>
            )}
            {/* Desktop buttons */}
            <div className="hidden sm:flex items-center space-x-2">
              {onEdit && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onEdit(shoot)}
                  className="h-8 w-8 p-0 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-950"
                  title="Edit shoot"
                >
                  <Edit className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </Button>
              )}
              {onDelete && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 hover:border-red-300 dark:hover:bg-red-950"
                  title="Delete shoot"
                >
                  {isDeleting ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600"></div>
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                </Button>
              )}
            </div>

            {/* Mobile dropdown menu */}
            <div className="sm:hidden">
              <DropdownMenu
                trigger={
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-8 w-8 p-0"
                  >
                    <MoreVertical className="w-4 h-4" />
                  </Button>
                }
              >
                {onStatusChange && shoot.status === 'scheduled' && (
                  <DropdownMenuItem onClick={() => onStatusChange(shoot, 'completed')}>
                    <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                    Mark Complete
                  </DropdownMenuItem>
                )}
                {onCancel && shoot.status === 'scheduled' && (
                  <DropdownMenuItem
                    onClick={() => onCancel(shoot)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                  >
                    <X className="w-4 h-4 mr-2" />
                    Cancel Shoot
                  </DropdownMenuItem>
                )}
                {onEdit && (
                  <DropdownMenuItem onClick={() => onEdit(shoot)}>
                    <Edit className="w-4 h-4 mr-2 text-blue-600" />
                    Edit Shoot
                  </DropdownMenuItem>
                )}
                {onDelete && (
                  <DropdownMenuItem
                    onClick={handleDelete}
                    disabled={isDeleting}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    {isDeleting ? 'Deleting...' : 'Delete Shoot'}
                  </DropdownMenuItem>
                )}
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-card rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 overflow-hidden border border-border">
      {/* Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-card-foreground mb-1">
              {shoot.project?.name}
            </h3>
            <div className="flex items-center text-sm text-muted-foreground mb-2">
              <Building className="w-4 h-4 mr-1" />
              {shoot.project?.client?.name}
            </div>
          </div>
          <div className={`flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(shoot.status)}`}>
            {getStatusIcon(shoot.status)}
            <span className="ml-1 capitalize">{shoot.status}</span>
          </div>
        </div>

        {/* Date and Time */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="w-4 h-4 mr-2" />
            <span className="font-medium">Scheduled:</span>
            <span className="ml-2">{formatDateTime(shoot.scheduled_date)}</span>
          </div>

          {shoot.actual_date && (
            <div className="flex items-center text-sm text-muted-foreground">
              <CheckCircle className="w-4 h-4 mr-2" />
              <span className="font-medium">Completed:</span>
              <span className="ml-2">{formatDateTime(shoot.actual_date)}</span>
            </div>
          )}

          {isUpcoming && (
            <div className="text-xs text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded">
              Upcoming shoot
            </div>
          )}

          {isPast && shoot.status === 'scheduled' && (
            <div className="text-xs text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-900/20 px-2 py-1 rounded">
              Overdue
            </div>
          )}
        </div>

        {/* Location */}
        {shoot.project?.location && (
          <div className="flex items-center text-sm text-muted-foreground mb-3">
            <MapPin className="w-4 h-4 mr-2" />
            <span className="truncate">{shoot.project.location}</span>
          </div>
        )}

        {/* Pilot */}
        {shoot.pilot && (
          <div className="flex items-center text-sm text-muted-foreground mb-3">
            <User className="w-4 h-4 mr-2" />
            <span>Pilot: {shoot.pilot.name}</span>
          </div>
        )}

        {/* Recurring Info */}
        {shoot.is_recurring && (
          <div className="flex items-center text-sm text-muted-foreground mb-3">
            <RotateCcw className="w-4 h-4 mr-2" />
            <span>Recurring: {shoot.recurring_pattern}</span>
          </div>
        )}

        {/* Notes */}
        {shoot.notes && (
          <div className="text-sm text-muted-foreground mb-3">
            <div className="flex items-start">
              <FileText className="w-4 h-4 mr-2 mt-0.5" />
              <p className="line-clamp-2">{shoot.notes}</p>
            </div>
          </div>
        )}

        {/* Weather Conditions */}
        {shoot.weather_conditions && (
          <div className="text-sm text-muted-foreground mb-3">
            <span className="font-medium">Weather:</span> {shoot.weather_conditions}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-4 bg-muted border-t border-border">
        <div className="flex items-center justify-between">
          <div className="text-xs text-muted-foreground">
            Created {formatDate(shoot.created_at)}
          </div>
          
          <div className="flex items-center space-x-2">
            {onStatusChange && shoot.status === 'scheduled' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onStatusChange(shoot, 'completed')}
                className="text-xs text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-950"
              >
                <CheckCircle className="w-3 h-3 mr-1" />
                Mark Complete
              </Button>
            )}
            {onCancel && shoot.status === 'scheduled' && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onCancel(shoot)}
                className="text-xs text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-950"
                title="Cancel shoot"
              >
                <X className="w-3 h-3 mr-1" />
                Cancel
              </Button>
            )}
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(shoot)}
                className="text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-950"
                title="Edit shoot"
              >
                <Edit className="w-3 h-3 mr-1" />
                Edit
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
