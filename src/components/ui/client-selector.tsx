'use client'

import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Modal } from '@/components/ui/modal'
import { ClientForm } from '@/components/forms/ClientForm'
import { useClients } from '@/hooks/useApi'
import { Search, Plus, Check, ChevronDown } from 'lucide-react'
import type { Client } from '@/types'

interface ClientSelectorProps {
  value: string
  onChange: (clientId: string) => void
  error?: string
  disabled?: boolean
  label?: string
  required?: boolean
}

export function ClientSelector({ 
  value, 
  onChange, 
  error, 
  disabled = false, 
  label = "Client",
  required = false 
}: ClientSelectorProps) {
  const { data: clients, loading: clientsLoading, refetch } = useClients()
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  const selectedClient = clients?.find(client => client.id === value)
  
  const filteredClients = clients?.filter(client =>
    client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.email?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
        setSearchTerm('')
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleClientSelect = (client: Client) => {
    onChange(client.id)
    setIsOpen(false)
    setSearchTerm('')
  }

  const handleCreateSuccess = (newClient: Client) => {
    setIsCreateModalOpen(false)
    refetch()
    onChange(newClient.id)
    setIsOpen(false)
    setSearchTerm('')
  }

  const handleInputClick = () => {
    if (!disabled) {
      setIsOpen(true)
      inputRef.current?.focus()
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value)
    if (!isOpen) setIsOpen(true)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false)
      setSearchTerm('')
    }
  }

  if (clientsLoading) {
    return (
      <div>
        <Label>{label} {required && '*'}</Label>
        <div className="mt-1 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          <span className="ml-2 text-muted-foreground">Loading clients...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <Label htmlFor="client-selector">{label} {required && '*'}</Label>
      
      <div className="relative mt-1">
        <div
          className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 cursor-pointer ${
            disabled ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          onClick={handleInputClick}
        >
          <input
            ref={inputRef}
            id="client-selector"
            type="text"
            value={isOpen ? searchTerm : (selectedClient?.name || '')}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={selectedClient ? selectedClient.name : "Search clients..."}
            className="flex-1 bg-transparent outline-none placeholder:text-muted-foreground"
            disabled={disabled}
          />
          <ChevronDown className={`w-4 h-4 text-muted-foreground transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>

        {isOpen && (
          <div className="absolute z-50 w-full mt-1 border border-border rounded-md shadow-lg max-h-60 overflow-auto"
               style={{ backgroundColor: '#000000' }}>
            {/* Search Results */}
            {filteredClients.length > 0 ? (
              <div className="py-1">
                {filteredClients.map((client) => (
                  <div
                    key={client.id}
                    className="flex items-center justify-between px-3 py-2 text-sm cursor-pointer hover:bg-gray-800"
                    onClick={() => handleClientSelect(client)}
                  >
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-white truncate">{client.name}</div>
                      {client.email && (
                        <div className="text-xs text-gray-300 truncate">{client.email}</div>
                      )}
                    </div>
                    {value === client.id && (
                      <Check className="w-4 h-4 text-blue-400 flex-shrink-0 ml-2" />
                    )}
                  </div>
                ))}
              </div>
            ) : searchTerm ? (
              <div className="py-2">
                <div className="px-3 py-2 text-sm text-gray-300">
                  No clients found for "{searchTerm}"
                </div>
                <div className="px-3 py-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setIsCreateModalOpen(true)
                      setIsOpen(false)
                    }}
                    className="w-full justify-start"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add new client "{searchTerm}"
                  </Button>
                </div>
              </div>
            ) : (
              <div className="py-2">
                <div className="px-3 py-2 text-sm text-gray-300">
                  Start typing to search clients...
                </div>
                <div className="px-3 py-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setIsCreateModalOpen(true)
                      setIsOpen(false)
                    }}
                    className="w-full justify-start"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    Add new client
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {error && (
        <p className="text-sm text-red-600 mt-1">{error}</p>
      )}

      {/* Create Client Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Add New Client"
        size="lg"
      >
        <ClientForm
          initialName={searchTerm}
          onSuccess={handleCreateSuccess}
          onCancel={() => setIsCreateModalOpen(false)}
        />
      </Modal>
    </div>
  )
}
