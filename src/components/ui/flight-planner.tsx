'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Plane, 
  MapPin, 
  Clock, 
  Wind, 
  Eye, 
  AlertTriangle,
  CheckCircle,
  Navigation,
  Ruler,
  Battery,
  Camera
} from 'lucide-react'

interface FlightPlan {
  id: string
  name: string
  location: string
  altitude: number
  duration: number
  distance: number
  waypoints: Array<{
    lat: number
    lng: number
    altitude: number
    action?: string
  }>
  weather: {
    windSpeed: number
    visibility: number
    temperature: number
  }
  restrictions: string[]
  equipment: {
    drone: string
    battery: number
    camera: string
  }
}

interface FlightPlannerProps {
  isOpen: boolean
  onClose: () => void
  location?: string
  coordinates?: { lat: number; lng: number }
}

export function FlightPlanner({ isOpen, onClose, location, coordinates }: FlightPlannerProps) {
  const [flightPlan, setFlightPlan] = useState<Partial<FlightPlan>>({
    name: '',
    location: location || '',
    altitude: 100,
    duration: 30,
    distance: 2.5,
    waypoints: coordinates ? [
      { lat: coordinates.lat, lng: coordinates.lng, altitude: 100, action: 'takeoff' },
      { lat: coordinates.lat + 0.001, lng: coordinates.lng + 0.001, altitude: 120, action: 'photo' },
      { lat: coordinates.lat - 0.001, lng: coordinates.lng + 0.001, altitude: 120, action: 'video' },
      { lat: coordinates.lat, lng: coordinates.lng, altitude: 100, action: 'landing' }
    ] : [],
    weather: {
      windSpeed: 8,
      visibility: 10,
      temperature: 25
    },
    restrictions: ['No-fly zone nearby', 'Airport within 5km'],
    equipment: {
      drone: 'DJI Mavic 3',
      battery: 85,
      camera: '4K Hasselblad'
    }
  })

  const [activeTab, setActiveTab] = useState<'route' | 'weather' | 'equipment' | 'restrictions'>('route')

  if (!isOpen) return null

  const getWeatherStatus = () => {
    const { windSpeed, visibility } = flightPlan.weather || {}
    if (!windSpeed || !visibility) return 'unknown'
    
    if (windSpeed > 15 || visibility < 5) return 'poor'
    if (windSpeed > 10 || visibility < 8) return 'fair'
    return 'good'
  }

  const weatherStatus = getWeatherStatus()

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-card rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                <Plane className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-card-foreground">Flight Planner</h2>
                <p className="text-sm text-muted-foreground">Plan your drone mission</p>
              </div>
            </div>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex h-[600px]">
          {/* Left Panel - Controls */}
          <div className="w-1/3 border-r border-gray-200 overflow-y-auto">
            <div className="p-4">
              {/* Basic Info */}
              <div className="space-y-4 mb-6">
                <div>
                  <Label htmlFor="flightName">Flight Plan Name</Label>
                  <Input
                    id="flightName"
                    value={flightPlan.name}
                    onChange={(e) => setFlightPlan(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter flight plan name"
                    className="mt-1"
                  />
                </div>
                <div>
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    value={flightPlan.location}
                    onChange={(e) => setFlightPlan(prev => ({ ...prev, location: e.target.value }))}
                    placeholder="Enter location"
                    className="mt-1"
                  />
                </div>
              </div>

              {/* Tabs */}
              <div className="flex space-x-1 bg-gray-100 rounded-lg p-1 mb-4">
                {[
                  { id: 'route', label: 'Route', icon: Navigation },
                  { id: 'weather', label: 'Weather', icon: Wind },
                  { id: 'equipment', label: 'Equipment', icon: Camera },
                  { id: 'restrictions', label: 'Restrictions', icon: AlertTriangle }
                ].map(tab => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex-1 flex items-center justify-center space-x-1 px-3 py-2 rounded-md text-xs font-medium transition-colors ${
                      activeTab === tab.id
                        ? 'bg-card text-card-foreground shadow'
                        : 'text-muted-foreground hover:text-foreground'
                    }`}
                  >
                    <tab.icon className="w-3 h-3" />
                    <span>{tab.label}</span>
                  </button>
                ))}
              </div>

              {/* Tab Content */}
              {activeTab === 'route' && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label htmlFor="altitude">Altitude (m)</Label>
                      <Input
                        id="altitude"
                        type="number"
                        value={flightPlan.altitude}
                        onChange={(e) => setFlightPlan(prev => ({ ...prev, altitude: Number(e.target.value) }))}
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor="duration">Duration (min)</Label>
                      <Input
                        id="duration"
                        type="number"
                        value={flightPlan.duration}
                        onChange={(e) => setFlightPlan(prev => ({ ...prev, duration: Number(e.target.value) }))}
                        className="mt-1"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label>Waypoints</Label>
                    <div className="mt-2 space-y-2">
                      {flightPlan.waypoints?.map((waypoint, index) => (
                        <div key={index} className="flex items-center space-x-2 text-sm">
                          <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium">
                            {index + 1}
                          </div>
                          <div className="flex-1">
                            <div className="font-medium">{waypoint.action || 'Waypoint'}</div>
                            <div className="text-muted-foreground">
                              {waypoint.lat.toFixed(4)}, {waypoint.lng.toFixed(4)} @ {waypoint.altitude}m
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="flex items-center space-x-2 text-blue-800 mb-2">
                      <Ruler className="w-4 h-4" />
                      <span className="font-medium">Flight Summary</span>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>
                        <span className="text-blue-700">Distance:</span>
                        <span className="font-medium ml-1">{flightPlan.distance} km</span>
                      </div>
                      <div>
                        <span className="text-blue-700">Duration:</span>
                        <span className="font-medium ml-1">{flightPlan.duration} min</span>
                      </div>
                      <div>
                        <span className="text-blue-700">Max Alt:</span>
                        <span className="font-medium ml-1">{flightPlan.altitude} m</span>
                      </div>
                      <div>
                        <span className="text-blue-700">Waypoints:</span>
                        <span className="font-medium ml-1">{flightPlan.waypoints?.length || 0}</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'weather' && (
                <div className="space-y-4">
                  <div className={`border rounded-lg p-3 ${
                    weatherStatus === 'good' ? 'border-green-200 bg-green-50' :
                    weatherStatus === 'fair' ? 'border-yellow-200 bg-yellow-50' :
                    'border-red-200 bg-red-50'
                  }`}>
                    <div className="flex items-center space-x-2 mb-2">
                      {weatherStatus === 'good' ? (
                        <CheckCircle className="w-4 h-4 text-green-600" />
                      ) : (
                        <AlertTriangle className="w-4 h-4 text-yellow-600" />
                      )}
                      <span className={`font-medium ${
                        weatherStatus === 'good' ? 'text-green-800' :
                        weatherStatus === 'fair' ? 'text-yellow-800' :
                        'text-red-800'
                      }`}>
                        Weather Conditions: {weatherStatus.toUpperCase()}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-3">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Wind className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Wind Speed</span>
                      </div>
                      <span className="text-sm font-bold">{flightPlan.weather?.windSpeed} km/h</span>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Eye className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Visibility</span>
                      </div>
                      <span className="text-sm font-bold">{flightPlan.weather?.visibility} km</span>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Clock className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Temperature</span>
                      </div>
                      <span className="text-sm font-bold">{flightPlan.weather?.temperature}°C</span>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'equipment' && (
                <div className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Plane className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Drone Model</span>
                      </div>
                      <span className="text-sm font-bold">{flightPlan.equipment?.drone}</span>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Battery className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Battery Level</span>
                      </div>
                      <span className={`text-sm font-bold ${
                        (flightPlan.equipment?.battery || 0) > 50 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                      }`}>
                        {flightPlan.equipment?.battery}%
                      </span>
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Camera className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Camera</span>
                      </div>
                      <span className="text-sm font-bold">{flightPlan.equipment?.camera}</span>
                    </div>
                  </div>

                  <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                    <div className="flex items-center space-x-2 text-orange-800 mb-2">
                      <Battery className="w-4 h-4" />
                      <span className="font-medium">Battery Estimate</span>
                    </div>
                    <div className="text-sm text-orange-700">
                      Estimated flight time: {flightPlan.duration} minutes<br />
                      Battery consumption: ~{Math.round((flightPlan.duration || 0) * 2)}%<br />
                      Remaining after flight: ~{(flightPlan.equipment?.battery || 0) - Math.round((flightPlan.duration || 0) * 2)}%
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'restrictions' && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    {flightPlan.restrictions?.map((restriction, index) => (
                      <div key={index} className="flex items-start space-x-2 p-3 border border-red-200 bg-red-50 rounded-lg">
                        <AlertTriangle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-red-800">{restriction}</span>
                      </div>
                    ))}
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                    <div className="flex items-center space-x-2 text-yellow-800 mb-2">
                      <AlertTriangle className="w-4 h-4" />
                      <span className="font-medium">Safety Checklist</span>
                    </div>
                    <div className="space-y-1 text-sm text-yellow-700">
                      <div>✓ Check weather conditions</div>
                      <div>✓ Verify no-fly zones</div>
                      <div>✓ Ensure battery levels</div>
                      <div>✓ Confirm equipment status</div>
                      <div>✓ Review flight path</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Right Panel - Map */}
          <div className="flex-1 bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center">
            <div className="text-center">
              <MapPin className="w-16 h-16 text-blue-600 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-card-foreground mb-2">Flight Path Visualization</h4>
              <p className="text-muted-foreground mb-4">
                Interactive map with flight path would be displayed here
              </p>
              <div className="bg-card rounded-lg p-4 shadow inline-block">
                <div className="text-sm text-muted-foreground mb-2">Current Location</div>
                <div className="font-medium text-card-foreground">{flightPlan.location || 'Not set'}</div>
                {coordinates && (
                  <div className="text-xs text-muted-foreground mt-1">
                    {coordinates.lat.toFixed(4)}, {coordinates.lng.toFixed(4)}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-border bg-muted">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Flight plan ready for execution
            </div>
            <div className="flex space-x-3">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button>
                <Plane className="w-4 h-4 mr-2" />
                Start Flight
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
