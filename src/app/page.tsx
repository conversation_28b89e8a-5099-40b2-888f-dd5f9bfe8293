'use client'

import { But<PERSON> } from "@/components/ui/button"
import { useAuth } from "@/contexts/AuthContext"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import Link from "next/link"

export default function Home() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    console.log('Home page useEffect:', { loading, user: !!user })
    if (!loading && user) {
      console.log('Redirecting to dashboard...')
      router.push('/dashboard')
    }
  }, [user, loading, router])

  if (loading) {
    console.log('Still loading...')
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading...</p>
          <p className="mt-2 text-xs text-muted-foreground">Debug: loading={loading.toString()}, user={user ? 'exists' : 'null'}</p>
        </div>
      </div>
    )
  }

  if (user) {
    return null // Will redirect to dashboard
  }
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-8">
      <div className="max-w-4xl mx-auto text-center">
        <div className="mb-8">
          <h1 className="text-5xl font-bold text-foreground mb-4">
            Welcome to Cymatics
          </h1>
          <p className="text-xl text-muted-foreground mb-8">
            Streamline your drone service operations with comprehensive project and client management
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <div className="bg-card rounded-lg p-6 shadow-lg border border-border">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mb-4 mx-auto">
              <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">Project Management</h3>
            <p className="text-muted-foreground">Organize and track all your drone projects with detailed client information and location mapping.</p>
          </div>

          <div className="bg-card rounded-lg p-6 shadow-lg border border-border">
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mb-4 mx-auto">
              <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">Shoot Scheduling</h3>
            <p className="text-muted-foreground">Schedule and manage drone shoots with automated reminders and recurring appointments.</p>
          </div>

          <div className="bg-card rounded-lg p-6 shadow-lg border border-border">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mb-4 mx-auto">
              <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">Financial Tracking</h3>
            <p className="text-muted-foreground">Monitor payments, expenses, and billing with GST support and comprehensive reporting.</p>
          </div>
        </div>

        <div className="flex justify-center">
          <Link href="/login">
            <Button variant="outline" size="lg" className="border-2">
              Sign In
            </Button>
          </Link>
        </div>

        <div className="mt-12 text-sm text-muted-foreground">
          <p>Built with Next.js, TypeScript, and Supabase</p>
        </div>
      </div>
    </div>
  );
}
