'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Modal } from '@/components/ui/modal'
import { PaymentForm } from '@/components/forms/PaymentForm'
import { ExpenseForm } from '@/components/forms/ExpenseForm'
import { PaymentCard } from '@/components/ui/payment-card'
import { ExpenseCard } from '@/components/ui/expense-card'
import { usePayments, useExpenses, useDeletePayment, useDeleteExpense } from '@/hooks/useApi'
import { Search, Plus, TrendingUp, TrendingDown, DollarSign, Calendar, Grid, List } from 'lucide-react'
import toast from 'react-hot-toast'
import type { Payment, Expense } from '@/types'

export default function FinancesPage() {
  const { data: payments, loading: paymentsLoading, refetch: refetchPayments } = usePayments()
  const { data: expenses, loading: expensesLoading, refetch: refetchExpenses } = useExpenses()
  const { deletePayment } = useDeletePayment()
  const { deleteExpense } = useDeleteExpense()
  const [searchTerm, setSearchTerm] = useState('')
  const [activeTab, setActiveTab] = useState<'payments' | 'expenses' | 'overview'>('overview')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false)
  const [isExpenseModalOpen, setIsExpenseModalOpen] = useState(false)
  const [editingPayment, setEditingPayment] = useState<Payment | null>(null)
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null)

  const loading = paymentsLoading || expensesLoading

  // Calculate financial summary
  const totalPayments = payments?.reduce((sum, payment) => sum + payment.amount, 0) || 0
  const totalExpenses = expenses?.reduce((sum, expense) => sum + expense.amount, 0) || 0
  const netIncome = totalPayments - totalExpenses

  // Filter data based on search
  const filteredPayments = payments?.filter(payment => 
    payment.project?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    payment.project?.client?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    payment.reference_number?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  const filteredExpenses = expenses?.filter(expense =>
    expense.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    expense.project?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    expense.category.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  const handlePaymentSuccess = () => {
    setIsPaymentModalOpen(false)
    setEditingPayment(null)
    refetchPayments()
  }

  const handleExpenseSuccess = () => {
    setIsExpenseModalOpen(false)
    setEditingExpense(null)
    refetchExpenses()
  }

  const handleDeletePayment = async (payment: Payment) => {
    try {
      await deletePayment(payment.id)
      toast.success('Payment deleted successfully')
      refetchPayments()
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete payment')
    }
  }

  const handleDeleteExpense = async (expense: Expense) => {
    try {
      await deleteExpense(expense.id)
      toast.success('Expense deleted successfully')
      refetchExpenses()
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete expense')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading financial data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Financial Management</h1>
          <p className="text-muted-foreground">Track payments and expenses</p>
        </div>
        <div className="flex space-x-3">
          <Button onClick={() => setIsExpenseModalOpen(true)} variant="outline">
            <Plus className="w-4 h-4 mr-2" />
            Add Expense
          </Button>
          <Button onClick={() => setIsPaymentModalOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Record Payment
          </Button>
        </div>
      </div>

      {/* Financial Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card rounded-lg shadow p-6 border border-border">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <TrendingUp className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Payments</p>
              <p className="text-2xl font-bold text-green-600">₹{totalPayments.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg shadow p-6 border border-border">
          <div className="flex items-center">
            <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
              <TrendingDown className="w-6 h-6 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Expenses</p>
              <p className="text-2xl font-bold text-red-600">₹{totalExpenses.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg shadow p-6 border border-border">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <DollarSign className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Net Income</p>
              <p className={`text-2xl font-bold ${netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                ₹{netIncome.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg shadow p-6 border border-border">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
              <Calendar className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">This Month</p>
              <p className="text-2xl font-bold text-purple-600">
                {payments?.filter(p => new Date(p.payment_date).getMonth() === new Date().getMonth()).length || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs and Controls */}
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        <div className="flex space-x-1 bg-muted rounded-lg p-1">
          <button
            onClick={() => setActiveTab('overview')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'overview'
                ? 'bg-card text-card-foreground shadow'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            Overview
          </button>
          <button
            onClick={() => setActiveTab('payments')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'payments'
                ? 'bg-card text-card-foreground shadow'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            Payments ({payments?.length || 0})
          </button>
          <button
            onClick={() => setActiveTab('expenses')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'expenses'
                ? 'bg-card text-card-foreground shadow'
                : 'text-muted-foreground hover:text-foreground'
            }`}
          >
            Expenses ({expenses?.length || 0})
          </button>
        </div>

        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search transactions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>

          {/* View Mode Toggle */}
          {activeTab !== 'overview' && (
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Payments */}
          <div className="bg-card rounded-lg shadow p-6 border border-border">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-card-foreground">Recent Payments</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setActiveTab('payments')}
              >
                View All
              </Button>
            </div>
            <div className="space-y-3">
              {filteredPayments.slice(0, 3).map((payment) => (
                <PaymentCard
                  key={payment.id}
                  payment={payment}
                  onEdit={setEditingPayment}
                  onDelete={handleDeletePayment}
                  compact
                />
              ))}
              {filteredPayments.length === 0 && (
                <p className="text-muted-foreground text-center py-4">No payments recorded yet</p>
              )}
            </div>
          </div>

          {/* Recent Expenses */}
          <div className="bg-card rounded-lg shadow p-6 border border-border">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-card-foreground">Recent Expenses</h3>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setActiveTab('expenses')}
              >
                View All
              </Button>
            </div>
            <div className="space-y-3">
              {filteredExpenses.slice(0, 3).map((expense) => (
                <ExpenseCard
                  key={expense.id}
                  expense={expense}
                  onEdit={setEditingExpense}
                  onDelete={handleDeleteExpense}
                  compact
                />
              ))}
              {filteredExpenses.length === 0 && (
                <p className="text-muted-foreground text-center py-4">No expenses recorded yet</p>
              )}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'payments' && (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
        }>
          {filteredPayments.map((payment) => (
            <PaymentCard
              key={payment.id}
              payment={payment}
              onEdit={setEditingPayment}
              onDelete={handleDeletePayment}
              compact={viewMode === 'list'}
            />
          ))}
          {filteredPayments.length === 0 && (
            <div className="col-span-full text-center py-12">
              <TrendingUp className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">No payments found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm ? 'Try adjusting your search terms' : 'Start by recording your first payment'}
              </p>
              {!searchTerm && (
                <Button onClick={() => setIsPaymentModalOpen(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Record Payment
                </Button>
              )}
            </div>
          )}
        </div>
      )}

      {activeTab === 'expenses' && (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
        }>
          {filteredExpenses.map((expense) => (
            <ExpenseCard
              key={expense.id}
              expense={expense}
              onEdit={setEditingExpense}
              onDelete={handleDeleteExpense}
              compact={viewMode === 'list'}
            />
          ))}
          {filteredExpenses.length === 0 && (
            <div className="col-span-full text-center py-12">
              <TrendingDown className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No expenses found</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm ? 'Try adjusting your search terms' : 'Start by recording your first expense'}
              </p>
              {!searchTerm && (
                <Button onClick={() => setIsExpenseModalOpen(true)}>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Expense
                </Button>
              )}
            </div>
          )}
        </div>
      )}

      {/* Payment Modal */}
      <Modal
        isOpen={isPaymentModalOpen || !!editingPayment}
        onClose={() => {
          setIsPaymentModalOpen(false)
          setEditingPayment(null)
        }}
        title={editingPayment ? 'Edit Payment' : 'Record New Payment'}
        size="lg"
      >
        <PaymentForm
          payment={editingPayment || undefined}
          onSuccess={handlePaymentSuccess}
          onCancel={() => {
            setIsPaymentModalOpen(false)
            setEditingPayment(null)
          }}
        />
      </Modal>

      {/* Expense Modal */}
      <Modal
        isOpen={isExpenseModalOpen || !!editingExpense}
        onClose={() => {
          setIsExpenseModalOpen(false)
          setEditingExpense(null)
        }}
        title={editingExpense ? 'Edit Expense' : 'Record New Expense'}
        size="lg"
      >
        <ExpenseForm
          expense={editingExpense || undefined}
          onSuccess={handleExpenseSuccess}
          onCancel={() => {
            setIsExpenseModalOpen(false)
            setEditingExpense(null)
          }}
        />
      </Modal>
    </div>
  )
}
