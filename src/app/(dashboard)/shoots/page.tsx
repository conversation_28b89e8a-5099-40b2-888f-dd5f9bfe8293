'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Modal } from '@/components/ui/modal'
import { ShootForm } from '@/components/forms/ShootForm'
import { ShootCard } from '@/components/ui/shoot-card'
import { ShootCompletionForm } from '@/components/ui/shoot-completion-form'
import { useShoots, useUpdateShoot, useDeleteShoot } from '@/hooks/useApi'
import { Search, Plus, Calendar, Filter, Grid, List } from 'lucide-react'
import toast from 'react-hot-toast'
import type { Shoot } from '@/types'

export default function ShootsPage() {
  const { data: shoots, loading, refetch } = useShoots()
  const { updateShoot } = useUpdateShoot()
  const { deleteShoot } = useDeleteShoot()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [dateFilter, setDateFilter] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [editingShoot, setEditingShoot] = useState<Shoot | null>(null)
  const [completingShoot, setCompletingShoot] = useState<Shoot | null>(null)

  const filteredShoots = shoots?.filter(shoot => {
    const matchesSearch = 
      shoot.project?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      shoot.project?.client?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      shoot.project?.location?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || shoot.status === statusFilter

    let matchesDate = true
    if (dateFilter !== 'all') {
      const shootDate = new Date(shoot.scheduled_date)
      const now = new Date()
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
      const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)
      const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)

      switch (dateFilter) {
        case 'today':
          matchesDate = shootDate >= today && shootDate < tomorrow
          break
        case 'upcoming':
          matchesDate = shootDate >= now
          break
        case 'this_week':
          matchesDate = shootDate >= today && shootDate < nextWeek
          break
        case 'overdue':
          matchesDate = shootDate < now && shoot.status === 'scheduled'
          break
      }
    }

    return matchesSearch && matchesStatus && matchesDate
  }) || []

  // Sort shoots by scheduled date
  const sortedShoots = filteredShoots.sort((a, b) => 
    new Date(a.scheduled_date).getTime() - new Date(b.scheduled_date).getTime()
  )

  const handleCreateSuccess = () => {
    setIsCreateModalOpen(false)
    refetch()
  }

  const handleEditSuccess = () => {
    setEditingShoot(null)
    refetch()
  }

  const handleDelete = async (shoot: Shoot) => {
    try {
      await deleteShoot(shoot.id)
      toast.success('Shoot deleted successfully')
      refetch()
    } catch (error: any) {
      toast.error(error.message || 'Failed to delete shoot')
    }
  }

  const handleStatusChange = async (shoot: Shoot, newStatus: string) => {
    if (newStatus === 'completed') {
      // Open completion form instead of directly marking as complete
      setCompletingShoot(shoot)
      return
    }

    try {
      const updates: any = { status: newStatus }

      await updateShoot(shoot.id, updates)
      toast.success(`Shoot marked as ${newStatus}`)
      refetch()
    } catch (error: any) {
      toast.error(error.message || 'Failed to update shoot status')
    }
  }



  const handleCancel = async (shoot: Shoot) => {
    if (!confirm(`Are you sure you want to cancel this shoot scheduled for ${new Date(shoot.scheduled_date).toLocaleDateString()}? This action can be undone by editing the shoot later.`)) {
      return
    }

    try {
      await handleStatusChange(shoot, 'cancelled')
    } catch (error: any) {
      toast.error(error.message || 'Failed to cancel shoot')
    }
  }

  const handleCompleteShoot = async (completionData: any) => {
    if (!completingShoot) return

    try {
      const updates = {
        status: 'completed',
        actual_date: new Date().toISOString(),
        device_used: completionData.device_used,
        battery_count: completionData.battery_count,
        shoot_start_time: completionData.shoot_start_time,
        shoot_end_time: completionData.shoot_end_time,
        completion_notes: completionData.completion_notes
      }

      await updateShoot(completingShoot.id, updates)
      toast.success('Shoot completed successfully')
      setCompletingShoot(null)
      refetch()
    } catch (error: any) {
      toast.error(error.message || 'Failed to complete shoot')
      throw error
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading shoots...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Shoots</h1>
          <p className="text-gray-600">Schedule and manage drone shoots</p>
        </div>
        <Button onClick={() => setIsCreateModalOpen(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Schedule Shoot
        </Button>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        <div className="flex flex-1 gap-4 max-w-4xl">
          {/* Search */}
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search shoots, projects, or clients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
          >
            <option value="all">All Status</option>
            <option value="scheduled">Scheduled</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
            <option value="rescheduled">Rescheduled</option>
          </select>

          {/* Date Filter */}
          <select
            value={dateFilter}
            onChange={(e) => setDateFilter(e.target.value)}
            className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
          >
            <option value="all">All Dates</option>
            <option value="today">Today</option>
            <option value="upcoming">Upcoming</option>
            <option value="this_week">This Week</option>
            <option value="overdue">Overdue</option>
          </select>
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center space-x-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid className="w-4 h-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Shoots Grid/List */}
      {sortedShoots.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
            <Calendar className="w-6 h-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {searchTerm || statusFilter !== 'all' || dateFilter !== 'all' ? 'No shoots found' : 'No shoots scheduled'}
          </h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || statusFilter !== 'all' || dateFilter !== 'all'
              ? 'Try adjusting your search terms or filters'
              : 'Get started by scheduling your first shoot'
            }
          </p>
          {!searchTerm && statusFilter === 'all' && dateFilter === 'all' && (
            <Button onClick={() => setIsCreateModalOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Schedule Shoot
            </Button>
          )}
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
        }>
          {sortedShoots.map((shoot) => (
            <ShootCard
              key={shoot.id}
              shoot={shoot}
              onEdit={setEditingShoot}
              onStatusChange={handleStatusChange}
              onCancel={handleCancel}
              compact={viewMode === 'list'}
            />
          ))}
        </div>
      )}

      {/* Stats Summary */}
      {sortedShoots.length > 0 && (
        <div className="bg-card rounded-lg shadow p-6 border border-border">
          <h3 className="text-lg font-medium text-card-foreground mb-4">Summary</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {sortedShoots.length}
              </div>
              <div className="text-sm text-muted-foreground">Total Shoots</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {sortedShoots.filter(s => s.status === 'completed').length}
              </div>
              <div className="text-sm text-muted-foreground">Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                {sortedShoots.filter(s => s.status === 'scheduled').length}
              </div>
              <div className="text-sm text-muted-foreground">Scheduled</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                {sortedShoots.filter(s =>
                  s.status === 'scheduled' && new Date(s.scheduled_date) < new Date()
                ).length}
              </div>
              <div className="text-sm text-muted-foreground">Overdue</div>
            </div>
          </div>
        </div>
      )}

      {/* Create Shoot Modal */}
      <Modal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        title="Schedule New Shoot"
        size="xl"
      >
        <ShootForm
          onSuccess={handleCreateSuccess}
          onCancel={() => setIsCreateModalOpen(false)}
        />
      </Modal>

      {/* Edit Shoot Modal */}
      <Modal
        isOpen={!!editingShoot}
        onClose={() => setEditingShoot(null)}
        title="Edit Shoot"
        size="xl"
      >
        {editingShoot && (
          <ShootForm
            shoot={editingShoot}
            onSuccess={handleEditSuccess}
            onCancel={() => setEditingShoot(null)}
          />
        )}
      </Modal>

      {/* Shoot Completion Form */}
      <ShootCompletionForm
        shoot={completingShoot}
        isOpen={!!completingShoot}
        onClose={() => setCompletingShoot(null)}
        onComplete={handleCompleteShoot}
      />
    </div>
  )
}
