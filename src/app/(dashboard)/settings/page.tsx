'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useTheme } from '@/contexts/ThemeContext'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import toast from 'react-hot-toast'
import {
  User,
  Palette,
  Shield,
  Activity,
  Users,
  Settings as SettingsIcon,
  Bell,
  Lock,
  Globe,
  Camera,
  Save,
  Mail,
  Phone,
  MapPin,
  Sun,
  Moon,
  Monitor,
  Check,
  UserPlus,
  UserMinus,
  Edit2,
  Trash2,
  MoreHorizontal,
  Search,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react'

const settingsTabs = [
  { id: 'profile', name: 'Profile', icon: User, description: 'Manage your personal information' },
  { id: 'appearance', name: 'Appearance', icon: Palette, description: 'Customize your interface' },
  { id: 'notifications', name: 'Notifications', icon: Bell, description: 'Configure your alerts' },
  { id: 'security', name: 'Security', icon: Lock, description: 'Manage your account security' },
  { id: 'admin', name: 'Admin Tools', icon: Shield, description: 'User management and system tools', adminOnly: true },
]

export default function SettingsPage() {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState('profile')

  // Filter tabs based on user role
  const availableTabs = settingsTabs.filter(tab => 
    !tab.adminOnly || (tab.adminOnly && user?.role === 'admin')
  )

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return <ProfileSettings />
      case 'appearance':
        return <AppearanceSettings />
      case 'notifications':
        return <NotificationSettings />
      case 'security':
        return <SecuritySettings />
      case 'admin':
        return user?.role === 'admin' ? <AdminSettings /> : null
      default:
        return <ProfileSettings />
    }
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center space-x-3 mb-2">
          <SettingsIcon className="w-8 h-8 text-foreground" />
          <h1 className="text-3xl font-bold text-foreground">Settings</h1>
        </div>
        <p className="text-muted-foreground">Manage your account settings and preferences</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar Navigation */}
        <div className="lg:col-span-1">
          <nav className="space-y-2">
            {availableTabs.map((tab) => {
              const isActive = activeTab === tab.id
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-start space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                    isActive
                      ? 'bg-primary/10 text-primary border border-primary/20'
                      : 'text-muted-foreground hover:bg-muted hover:text-foreground'
                  }`}
                >
                  <tab.icon className={`w-5 h-5 mt-0.5 ${isActive ? 'text-primary' : 'text-muted-foreground'}`} />
                  <div>
                    <div className={`font-medium ${isActive ? 'text-primary' : 'text-foreground'}`}>
                      {tab.name}
                    </div>
                    <div className={`text-sm ${isActive ? 'text-primary/80' : 'text-muted-foreground'}`}>
                      {tab.description}
                    </div>
                  </div>
                </button>
              )
            })}
          </nav>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          <div className="bg-card rounded-lg shadow-sm border border-border p-6">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  )
}

// Profile Settings Schema
const profileSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  address: z.string().optional(),
  bio: z.string().optional(),
})

type ProfileFormData = z.infer<typeof profileSchema>

function ProfileSettings() {
  const { user } = useAuth()
  const [isLoading, setIsLoading] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: user?.name || '',
      email: user?.email || '',
      phone: user?.phone || '',
      address: user?.address || '',
      bio: user?.bio || '',
    },
  })

  const onSubmit = async (data: ProfileFormData) => {
    try {
      setIsLoading(true)
      // TODO: Implement profile update API call
      console.log('Profile update data:', data)
      toast.success('Profile updated successfully')
    } catch (error: any) {
      toast.error(error.message || 'Failed to update profile')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-card-foreground">Profile Settings</h2>
          <p className="text-muted-foreground">Update your personal information and profile details</p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Profile Picture Section */}
        <div className="flex items-center space-x-6">
          <div className="relative">
            <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center">
              <span className="text-primary font-medium text-2xl">
                {user?.name?.charAt(0).toUpperCase()}
              </span>
            </div>
            <button
              type="button"
              className="absolute bottom-0 right-0 bg-primary text-primary-foreground rounded-full p-2 hover:bg-primary/90 transition-colors"
            >
              <Camera className="w-4 h-4" />
            </button>
          </div>
          <div>
            <h3 className="text-sm font-medium text-card-foreground">Profile Picture</h3>
            <p className="text-sm text-muted-foreground">Click the camera icon to upload a new picture</p>
          </div>
        </div>

        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label htmlFor="name">Full Name *</Label>
            <Input
              id="name"
              {...register('name')}
              className="mt-1"
              placeholder="Enter your full name"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-destructive">{errors.name.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="email">Email Address *</Label>
            <Input
              id="email"
              type="email"
              {...register('email')}
              className="mt-1"
              placeholder="Enter your email"
            />
            {errors.email && (
              <p className="mt-1 text-sm text-destructive">{errors.email.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              {...register('phone')}
              className="mt-1"
              placeholder="Enter your phone number"
            />
          </div>

          <div>
            <Label htmlFor="role">Role</Label>
            <Input
              id="role"
              value={user?.role || ''}
              disabled
              className="mt-1 bg-muted"
              placeholder="Your role"
            />
            <p className="mt-1 text-sm text-muted-foreground">Contact admin to change your role</p>
          </div>
        </div>

        {/* Address */}
        <div>
          <Label htmlFor="address">Address</Label>
          <Input
            id="address"
            {...register('address')}
            className="mt-1"
            placeholder="Enter your address"
          />
        </div>

        {/* Bio */}
        <div>
          <Label htmlFor="bio">Bio</Label>
          <textarea
            id="bio"
            {...register('bio')}
            rows={4}
            className="mt-1 block w-full rounded-md border border-input bg-background px-3 py-2 text-sm text-foreground placeholder:text-muted-foreground focus:border-ring focus:outline-none focus:ring-1 focus:ring-ring"
            placeholder="Tell us about yourself..."
          />
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={() => reset()}
          >
            Reset
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
            className="flex items-center space-x-2"
          >
            <Save className="w-4 h-4" />
            <span>{isLoading ? 'Saving...' : 'Save Changes'}</span>
          </Button>
        </div>
      </form>
    </div>
  )
}

function AppearanceSettings() {
  const { theme, setTheme, isDark } = useTheme()

  const themeOptions = [
    {
      value: 'light',
      label: 'Light',
      description: 'Light mode with bright colors',
      icon: Sun,
    },
    {
      value: 'dark',
      label: 'Dark',
      description: 'Dark mode with darker colors',
      icon: Moon,
    },
    {
      value: 'system',
      label: 'System',
      description: 'Follow your system preference',
      icon: Monitor,
    },
  ]

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-card-foreground mb-2">Appearance</h2>
        <p className="text-muted-foreground">Customize how the interface looks and feels</p>
      </div>

      {/* Theme Selection */}
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-card-foreground mb-4">Theme</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {themeOptions.map((option) => {
              const isSelected = theme === option.value
              const IconComponent = option.icon

              return (
                <button
                  key={option.value}
                  onClick={() => setTheme(option.value as 'light' | 'dark' | 'system')}
                  className={`relative p-4 rounded-lg border-2 transition-all ${
                    isSelected
                      ? 'border-primary bg-primary/10'
                      : 'border-border hover:border-border/80'
                  }`}
                >
                  {isSelected && (
                    <div className="absolute top-2 right-2">
                      <div className="bg-primary text-primary-foreground rounded-full p-1">
                        <Check className="w-3 h-3" />
                      </div>
                    </div>
                  )}

                  <div className="flex flex-col items-center space-y-3">
                    <div className={`p-3 rounded-full ${
                      isSelected
                        ? 'bg-primary/20 text-primary'
                        : 'bg-muted text-muted-foreground'
                    }`}>
                      <IconComponent className="w-6 h-6" />
                    </div>

                    <div className="text-center">
                      <div className={`font-medium ${
                        isSelected
                          ? 'text-primary'
                          : 'text-card-foreground'
                      }`}>
                        {option.label}
                      </div>
                      <div className={`text-sm ${
                        isSelected
                          ? 'text-primary/80'
                          : 'text-muted-foreground'
                      }`}>
                        {option.description}
                      </div>
                    </div>
                  </div>
                </button>
              )
            })}
          </div>
        </div>

        {/* Current Theme Preview */}
        <div className="border border-border rounded-lg p-4">
          <h4 className="font-medium text-card-foreground mb-3">Preview</h4>
          <div className="bg-muted rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-card-foreground">Current theme:</span>
              <span className="text-primary font-medium capitalize">
                {theme} {isDark ? '(Dark)' : '(Light)'}
              </span>
            </div>
            <div className="h-2 bg-primary/20 rounded-full">
              <div className="h-2 bg-primary rounded-full w-3/4"></div>
            </div>
            <div className="text-sm text-muted-foreground">
              This is how your interface will look with the selected theme.
            </div>
          </div>
        </div>

        {/* Additional Settings */}
        <div>
          <h3 className="text-lg font-medium text-card-foreground mb-4">Display Options</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-card-foreground">Compact Mode</div>
                <div className="text-sm text-muted-foreground">Use a more compact layout to fit more content</div>
              </div>
              <button className="relative inline-flex h-6 w-11 items-center rounded-full border-2 bg-gray-200 border-gray-300 dark:bg-muted dark:border-border transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2">
                <span className="inline-block h-4 w-4 transform rounded-full shadow-lg transition-all translate-x-0.5 bg-gray-400 border-gray-400 dark:bg-gray-600 dark:border-gray-600"></span>
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-card-foreground">Reduced Motion</div>
                <div className="text-sm text-muted-foreground">Minimize animations and transitions</div>
              </div>
              <button className="relative inline-flex h-6 w-11 items-center rounded-full border-2 bg-gray-200 border-gray-300 dark:bg-muted dark:border-border transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2">
                <span className="inline-block h-4 w-4 transform rounded-full shadow-lg transition-all translate-x-0.5 bg-gray-400 border-gray-400 dark:bg-gray-600 dark:border-gray-600"></span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function NotificationSettings() {
  const [emailNotifications, setEmailNotifications] = useState(true)
  const [pushNotifications, setPushNotifications] = useState(false)
  const [smsNotifications, setSmsNotifications] = useState(false)

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-card-foreground mb-2">Notifications</h2>
        <p className="text-muted-foreground">Configure how you receive notifications</p>
      </div>

      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-card-foreground mb-4">Notification Channels</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-card-foreground">Email Notifications</div>
                <div className="text-sm text-muted-foreground">Receive notifications via email</div>
              </div>
              <button
                onClick={() => setEmailNotifications(!emailNotifications)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full border-2 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 ${
                  emailNotifications
                    ? 'bg-primary border-primary'
                    : 'bg-gray-200 border-gray-300 dark:bg-muted dark:border-border'
                }`}
              >
                <span className={`inline-block h-4 w-4 transform rounded-full shadow-lg transition-all ${
                  emailNotifications
                    ? 'translate-x-5 bg-blue-600 border-blue-600'
                    : 'translate-x-0.5 bg-gray-400 border-gray-400 dark:bg-gray-600 dark:border-gray-600'
                }`} />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-card-foreground">Push Notifications</div>
                <div className="text-sm text-muted-foreground">Receive browser push notifications</div>
              </div>
              <button
                onClick={() => setPushNotifications(!pushNotifications)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full border-2 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 ${
                  pushNotifications
                    ? 'bg-primary border-primary'
                    : 'bg-gray-200 border-gray-300 dark:bg-muted dark:border-border'
                }`}
              >
                <span className={`inline-block h-4 w-4 transform rounded-full shadow-lg transition-all ${
                  pushNotifications
                    ? 'translate-x-5 bg-blue-600 border-blue-600'
                    : 'translate-x-0.5 bg-gray-400 border-gray-400 dark:bg-gray-600 dark:border-gray-600'
                }`} />
              </button>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-card-foreground">SMS Notifications</div>
                <div className="text-sm text-muted-foreground">Receive notifications via SMS</div>
              </div>
              <button
                onClick={() => setSmsNotifications(!smsNotifications)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full border-2 transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 ${
                  smsNotifications
                    ? 'bg-primary border-primary'
                    : 'bg-gray-200 border-gray-300 dark:bg-muted dark:border-border'
                }`}
              >
                <span className={`inline-block h-4 w-4 transform rounded-full shadow-lg transition-all ${
                  smsNotifications
                    ? 'translate-x-5 bg-blue-600 border-blue-600'
                    : 'translate-x-0.5 bg-gray-400 border-gray-400 dark:bg-gray-600 dark:border-gray-600'
                }`} />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function SecuritySettings() {
  const [currentPassword, setCurrentPassword] = useState('')
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-card-foreground mb-2">Security</h2>
        <p className="text-muted-foreground">Manage your account security and password</p>
      </div>

      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-card-foreground mb-4">Change Password</h3>
          <div className="space-y-4 max-w-md">
            <div>
              <Label htmlFor="current-password">Current Password</Label>
              <Input
                id="current-password"
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="new-password">New Password</Label>
              <Input
                id="new-password"
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="confirm-password">Confirm New Password</Label>
              <Input
                id="confirm-password"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="mt-1"
              />
            </div>
            <Button className="w-full">Update Password</Button>
          </div>
        </div>

        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Two-Factor Authentication</h3>
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <Lock className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
              <div>
                <div className="font-medium text-yellow-800 dark:text-yellow-200">Two-factor authentication is disabled</div>
                <div className="text-sm text-yellow-700 dark:text-yellow-300">Add an extra layer of security to your account</div>
              </div>
            </div>
            <Button variant="outline" className="mt-3">Enable 2FA</Button>
          </div>
        </div>
      </div>
    </div>
  )
}

function AdminSettings() {
  const [activeAdminTab, setActiveAdminTab] = useState('users')
  const [searchTerm, setSearchTerm] = useState('')

  // Mock data - in real app, this would come from API
  const mockUsers = [
    { id: '1', name: 'John Doe', email: '<EMAIL>', role: 'admin', status: 'active', lastActive: '2024-01-20' },
    { id: '2', name: 'Jane Smith', email: '<EMAIL>', role: 'pilot', status: 'active', lastActive: '2024-01-19' },
    { id: '3', name: 'Mike Johnson', email: '<EMAIL>', role: 'operator', status: 'inactive', lastActive: '2024-01-15' },
  ]

  const mockActivity = [
    { id: '1', user: 'John Doe', action: 'Created new project', target: 'Luxury Villa Photography', timestamp: '2024-01-20 10:30 AM' },
    { id: '2', user: 'Jane Smith', action: 'Completed shoot', target: 'Resort Promotional Video', timestamp: '2024-01-20 09:15 AM' },
    { id: '3', user: 'Mike Johnson', action: 'Updated client information', target: 'Acme Real Estate', timestamp: '2024-01-19 04:45 PM' },
    { id: '4', user: 'John Doe', action: 'Added new expense', target: '₹2,500 - Equipment maintenance', timestamp: '2024-01-19 02:20 PM' },
  ]

  const adminTabs = [
    { id: 'users', name: 'User Management', icon: Users },
    { id: 'activity', name: 'Recent Activity', icon: Activity },
  ]

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-card-foreground mb-2">Admin Tools</h2>
        <p className="text-muted-foreground">Manage users and monitor system activity</p>
      </div>

      {/* Admin Tabs */}
      <div className="border-b border-border mb-6">
        <nav className="-mb-px flex space-x-8">
          {adminTabs.map((tab) => {
            const isActive = activeAdminTab === tab.id
            return (
              <button
                key={tab.id}
                onClick={() => setActiveAdminTab(tab.id)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                  isActive
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                <span>{tab.name}</span>
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeAdminTab === 'users' && (
        <div className="space-y-6">
          {/* User Management Header */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-card-foreground">Users</h3>
              <p className="text-sm text-muted-foreground">Manage user accounts and permissions</p>
            </div>
            <Button className="flex items-center space-x-2">
              <UserPlus className="w-4 h-4" />
              <span>Add User</span>
            </Button>
          </div>

          {/* Search and Filters */}
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline" className="flex items-center space-x-2">
              <Filter className="w-4 h-4" />
              <span>Filter</span>
            </Button>
            <Button variant="outline" className="flex items-center space-x-2">
              <Download className="w-4 h-4" />
              <span>Export</span>
            </Button>
          </div>

          {/* Users Table */}
          <div className="bg-card border border-border rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-border">
              <thead className="bg-muted/50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Role
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Last Active
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-card divide-y divide-border">
                {mockUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-muted/50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                          <span className="text-primary font-medium text-sm">
                            {user.name.charAt(0)}
                          </span>
                        </div>
                        <div className="ml-3">
                          <div className="text-sm font-medium text-card-foreground">{user.name}</div>
                          <div className="text-sm text-muted-foreground">{user.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.role === 'admin'
                          ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300'
                          : user.role === 'pilot'
                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300'
                          : 'bg-muted text-muted-foreground'
                      }`}>
                        {user.role}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.status === 'active'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                          : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300'
                      }`}>
                        {user.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                      {user.lastActive}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <Button variant="outline" size="sm">
                          <Edit2 className="w-4 h-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {activeAdminTab === 'activity' && (
        <div className="space-y-6">
          {/* Activity Header */}
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">Recent Activity</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">Monitor user actions and system events</p>
            </div>
            <Button variant="outline" className="flex items-center space-x-2">
              <RefreshCw className="w-4 h-4" />
              <span>Refresh</span>
            </Button>
          </div>

          {/* Activity Feed */}
          <div className="bg-card border border-border rounded-lg">
            <div className="p-6">
              <div className="flow-root">
                <ul className="-mb-8">
                  {mockActivity.map((activity, index) => (
                    <li key={activity.id}>
                      <div className="relative pb-8">
                        {index !== mockActivity.length - 1 && (
                          <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-border" />
                        )}
                        <div className="relative flex space-x-3">
                          <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                            <Activity className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div className="min-w-0 flex-1 pt-1.5">
                            <div>
                              <p className="text-sm text-gray-900 dark:text-gray-100">
                                <span className="font-medium">{activity.user}</span> {activity.action}{' '}
                                <span className="font-medium">{activity.target}</span>
                              </p>
                              <p className="text-sm text-gray-500 dark:text-gray-400">{activity.timestamp}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
