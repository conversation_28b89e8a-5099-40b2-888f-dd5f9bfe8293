'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FlightPlanner } from '@/components/ui/flight-planner'
import { LocationAnalytics } from '@/components/ui/location-analytics'
import { useProjects, useShoots } from '@/hooks/useApi'
import {
  MapPin,
  Navigation,
  Layers,
  Filter,
  Search,
  Calendar,
  Building,
  Camera,
  Plane,
  AlertTriangle,
  CheckCircle,
  Circle
} from 'lucide-react'
import type { Project, Shoot } from '@/types'

interface MapLocation {
  id: string
  name: string
  type: 'project' | 'shoot'
  status: string
  location: string
  coordinates?: { lat: number; lng: number }
  date?: string
  project?: Project
  shoot?: Shoot
}

export default function MapPage() {
  const { data: projects, loading: projectsLoading } = useProjects()
  const { data: shoots, loading: shootsLoading } = useShoots()
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<'all' | 'project' | 'shoot'>('all')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | null>(null)
  const [mapLocations, setMapLocations] = useState<MapLocation[]>([])
  const [isFlightPlannerOpen, setIsFlightPlannerOpen] = useState(false)
  const [isAnalyticsOpen, setIsAnalyticsOpen] = useState(false)

  // Mock coordinates for demonstration (in real app, these would come from geocoding)
  const mockCoordinates = {
    'Bandra West, Mumbai': { lat: 19.0596, lng: 72.8295 },
    'Lonavala, Maharashtra': { lat: 18.7537, lng: 73.4068 },
    'Pune, Maharashtra': { lat: 18.5204, lng: 73.8567 },
    'Andheri East, Mumbai': { lat: 19.1136, lng: 72.8697 },
    'Goa': { lat: 15.2993, lng: 74.1240 },
    'Nashik, Maharashtra': { lat: 19.9975, lng: 73.7898 }
  }

  useEffect(() => {
    const locations: MapLocation[] = []

    // Add projects as locations
    projects?.forEach(project => {
      locations.push({
        id: `project-${project.id}`,
        name: project.name,
        type: 'project',
        status: project.status,
        location: project.location,
        coordinates: mockCoordinates[project.location as keyof typeof mockCoordinates],
        project
      })
    })

    // Add shoots as locations
    shoots?.forEach(shoot => {
      locations.push({
        id: `shoot-${shoot.id}`,
        name: shoot.title,
        type: 'shoot',
        status: shoot.status,
        location: shoot.location,
        date: shoot.scheduled_date,
        coordinates: mockCoordinates[shoot.location as keyof typeof mockCoordinates],
        shoot
      })
    })

    setMapLocations(locations)
  }, [projects, shoots])

  // Filter locations
  const filteredLocations = mapLocations.filter(location => {
    const matchesSearch = location.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         location.location.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === 'all' || location.type === filterType
    const matchesStatus = filterStatus === 'all' || location.status === filterStatus
    
    return matchesSearch && matchesType && matchesStatus
  })

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'active':
      case 'scheduled':
        return <Calendar className="w-4 h-4 text-blue-600" />
      case 'cancelled':
        return <AlertTriangle className="w-4 h-4 text-red-600" />
      default:
        return <Circle className="w-4 h-4 text-muted-foreground" />
    }
  }

  const getTypeIcon = (type: string) => {
    return type === 'project' ? 
      <Building className="w-4 h-4" /> : 
      <Camera className="w-4 h-4" />
  }

  if (projectsLoading || shootsLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading map data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Map & Locations</h1>
          <p className="text-muted-foreground">Visualize projects and shoots geographically</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" onClick={() => setIsFlightPlannerOpen(true)}>
            <Navigation className="w-4 h-4 mr-2" />
            Flight Planner
          </Button>
          <Button variant="outline">
            <Layers className="w-4 h-4 mr-2" />
            Layers
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-card rounded-lg shadow p-6 border border-border">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <MapPin className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Locations</p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{mapLocations.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg shadow p-6 border border-border">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <Building className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Active Projects</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                {mapLocations.filter(l => l.type === 'project' && l.status === 'active').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg shadow p-6 border border-border">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
              <Camera className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Upcoming Shoots</p>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {mapLocations.filter(l => l.type === 'shoot' && l.status === 'scheduled').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-card rounded-lg shadow p-6 border border-border">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
              <Plane className="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Flight Hours</p>
              <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">127</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="Search locations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>

          {/* Type Filter */}
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as any)}
            className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
          >
            <option value="all">All Types</option>
            <option value="project">Projects</option>
            <option value="shoot">Shoots</option>
          </select>

          {/* Status Filter */}
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="scheduled">Scheduled</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        {/* Active Filters */}
        {(filterType !== 'all' || filterStatus !== 'all' || searchTerm) && (
          <div className="flex items-center space-x-2 text-sm">
            <Filter className="w-4 h-4 text-muted-foreground" />
            <span className="text-muted-foreground">Active filters:</span>
            {filterType !== 'all' && (
              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                Type: {filterType}
              </span>
            )}
            {filterStatus !== 'all' && (
              <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                Status: {filterStatus}
              </span>
            )}
            {searchTerm && (
              <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs">
                Search: "{searchTerm}"
              </span>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                setFilterType('all')
                setFilterStatus('all')
                setSearchTerm('')
              }}
              className="text-xs"
            >
              Clear all
            </Button>
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Map Placeholder */}
        <div className="lg:col-span-2">
          <div className="bg-card rounded-lg shadow overflow-hidden border border-border">
            <div className="p-4 border-b border-border">
              <h3 className="text-lg font-semibold text-card-foreground">Interactive Map</h3>
              <p className="text-sm text-muted-foreground">Click on markers to view location details</p>
            </div>
            <div className="h-96 bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center relative">
              {/* Map placeholder with location markers */}
              <div className="text-center">
                <MapPin className="w-16 h-16 text-blue-600 dark:text-blue-400 mx-auto mb-4" />
                <h4 className="text-lg font-medium text-card-foreground mb-2">Interactive Map View</h4>
                <p className="text-muted-foreground mb-4">
                  Map integration with Google Maps or Mapbox would be implemented here
                </p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="bg-card rounded-lg p-3 shadow border border-border">
                    <div className="flex items-center text-blue-600 dark:text-blue-400 mb-1">
                      <Building className="w-4 h-4 mr-1" />
                      Projects
                    </div>
                    <div className="text-lg font-bold text-card-foreground">
                      {mapLocations.filter(l => l.type === 'project').length}
                    </div>
                  </div>
                  <div className="bg-card rounded-lg p-3 shadow border border-border">
                    <div className="flex items-center text-purple-600 dark:text-purple-400 mb-1">
                      <Camera className="w-4 h-4 mr-1" />
                      Shoots
                    </div>
                    <div className="text-lg font-bold text-card-foreground">
                      {mapLocations.filter(l => l.type === 'shoot').length}
                    </div>
                  </div>
                </div>
              </div>

              {/* Sample location markers */}
              <div className="absolute top-4 left-4 bg-red-500 rounded-full w-3 h-3 animate-pulse"></div>
              <div className="absolute top-12 right-8 bg-blue-500 rounded-full w-3 h-3 animate-pulse"></div>
              <div className="absolute bottom-8 left-12 bg-green-500 rounded-full w-3 h-3 animate-pulse"></div>
              <div className="absolute bottom-4 right-4 bg-purple-500 rounded-full w-3 h-3 animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Location List */}
        <div className="space-y-4">
          <div className="bg-card rounded-lg shadow border border-border">
            <div className="p-4 border-b border-border">
              <h3 className="text-lg font-semibold text-card-foreground">Locations</h3>
              <p className="text-sm text-muted-foreground">{filteredLocations.length} locations found</p>
            </div>
            <div className="max-h-96 overflow-y-auto">
              {filteredLocations.map((location) => (
                <div
                  key={location.id}
                  className={`p-4 border-b border-border hover:bg-muted cursor-pointer transition-colors ${
                    selectedLocation?.id === location.id ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800' : ''
                  }`}
                  onClick={() => setSelectedLocation(location)}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      {getTypeIcon(location.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="text-sm font-medium text-card-foreground truncate">
                          {location.name}
                        </h4>
                        {getStatusIcon(location.status)}
                      </div>
                      <div className="flex items-center text-xs text-muted-foreground mb-1">
                        <MapPin className="w-3 h-3 mr-1" />
                        {location.location}
                      </div>
                      {location.date && (
                        <div className="flex items-center text-xs text-muted-foreground">
                          <Calendar className="w-3 h-3 mr-1" />
                          {new Date(location.date).toLocaleDateString()}
                        </div>
                      )}
                      <div className="mt-1">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          location.type === 'project'
                            ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400'
                            : 'bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-400'
                        }`}>
                          {location.type}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              {filteredLocations.length === 0 && (
                <div className="p-8 text-center">
                  <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-card-foreground mb-2">No locations found</h4>
                  <p className="text-muted-foreground">
                    {searchTerm || filterType !== 'all' || filterStatus !== 'all'
                      ? 'Try adjusting your search terms or filters'
                      : 'No projects or shoots have been created yet'
                    }
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Selected Location Details */}
          {selectedLocation && (
            <div className="bg-card rounded-lg shadow p-4 border border-border">
              <h3 className="text-lg font-semibold text-card-foreground mb-3">Location Details</h3>
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Name</label>
                  <p className="text-sm text-card-foreground">{selectedLocation.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Type</label>
                  <p className="text-sm text-card-foreground capitalize">{selectedLocation.type}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Status</label>
                  <p className="text-sm text-card-foreground capitalize">{selectedLocation.status}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Location</label>
                  <p className="text-sm text-card-foreground">{selectedLocation.location}</p>
                </div>
                {selectedLocation.coordinates && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Coordinates</label>
                    <p className="text-sm text-card-foreground">
                      {selectedLocation.coordinates.lat.toFixed(4)}, {selectedLocation.coordinates.lng.toFixed(4)}
                    </p>
                  </div>
                )}
                {selectedLocation.date && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Date</label>
                    <p className="text-sm text-card-foreground">
                      {new Date(selectedLocation.date).toLocaleDateString()}
                    </p>
                  </div>
                )}
                <div className="pt-2">
                  <div className="space-y-2">
                    <Button size="sm" className="w-full">
                      <Navigation className="w-4 h-4 mr-2" />
                      Get Directions
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="w-full"
                      onClick={() => setIsAnalyticsOpen(true)}
                    >
                      View Analytics
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Flight Planner Modal */}
      <FlightPlanner
        isOpen={isFlightPlannerOpen}
        onClose={() => setIsFlightPlannerOpen(false)}
        location={selectedLocation?.location}
        coordinates={selectedLocation?.coordinates}
      />

      {/* Location Analytics Modal */}
      <LocationAnalytics
        isOpen={isAnalyticsOpen}
        onClose={() => setIsAnalyticsOpen(false)}
        locationName={selectedLocation?.location}
      />
    </div>
  )
}
