'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Modal } from '@/components/ui/modal'
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/dropdown-menu'
import { ProjectForm } from '@/components/forms/ProjectForm'
import { ShootForm } from '@/components/forms/ShootForm'
import { PaymentForm } from '@/components/forms/PaymentForm'
import { TaskForm } from '@/components/forms/TaskForm'
import { VendorPaymentForm } from '@/components/forms/VendorPaymentForm'
import { ShootCompletionForm, type ShootCompletionData } from '@/components/ui/shoot-completion-form'
import { AddExpenseForm } from '@/components/forms/AddExpenseForm'
import { ExpenseCard } from '@/components/ui/expense-card'
import { projectsApi, paymentsApi, shootsApi, tasksApi } from '@/lib/api'
import {
  AlertCircle,
  ArrowLeft,
  ArrowUpDown,
  Building,
  Calendar,
  CheckCircle,
  Clock,
  DollarSign,
  Edit,
  ExternalLink,
  Filter,
  MapPin,
  MoreVertical,
  Plus,
  Receipt,
  TrendingUp,
  User,
  X
} from 'lucide-react'
import toast from 'react-hot-toast'
import type { Project, Payment, Shoot, Task } from '@/types'
import Link from 'next/link'
import { formatGoogleMapsUrl } from '@/lib/google-maps-utils'
import { useProjectExpenses } from '@/hooks/useProjectExpenses'
import { calculateProjectTotal } from '@/lib/project-calculations'

export default function ProjectDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const projectId = params.id as string

  const [project, setProject] = useState<Project | null>(null)
  const [payments, setPayments] = useState<Payment[]>([])
  const [shoots, setShoots] = useState<Shoot[]>([])
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(true)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isAddShootModalOpen, setIsAddShootModalOpen] = useState(false)
  const [editingShoot, setEditingShoot] = useState<Shoot | null>(null)
  const [completingShoot, setCompletingShoot] = useState<Shoot | null>(null)
  const [isAddPaymentModalOpen, setIsAddPaymentModalOpen] = useState(false)
  const [isAddTaskModalOpen, setIsAddTaskModalOpen] = useState(false)
  const [isVendorPaymentModalOpen, setIsVendorPaymentModalOpen] = useState(false)
  const [isAddExpenseModalOpen, setIsAddExpenseModalOpen] = useState(false)
  const [shootSortBy, setShootSortBy] = useState<'date' | 'status' | 'amount'>('date')
  const [shootSortOrder, setShootSortOrder] = useState<'asc' | 'desc'>('desc')
  const [shootStatusFilter, setShootStatusFilter] = useState<string>('all')
  const { expenses, loading: expensesLoading, error: expensesError, refetch: refetchExpenses } = useProjectExpenses(projectId)
  const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0)
  const calculation = project ? calculateProjectTotal(shoots, project.client || { id: '', name: '', has_gst: false, created_at: '', updated_at: '' }, totalExpenses) : { profit: 0, outsourcing: 0 }
  const { profit, outsourcing: totalOutsourcing } = calculation

  const fetchProjectData = async () => {
    try {
      setLoading(true)
      const [projectData, paymentsData, shootsData, tasksData] = await Promise.all([
        projectsApi.getById(projectId),
        paymentsApi.getAll(),
        shootsApi.getAll(),
        tasksApi.getAll()
      ])
      
      setProject(projectData)
      
      // Filter data for this project
      const projectPayments = paymentsData.filter(payment => payment.project_id === projectId)
      const projectShoots = shootsData.filter(shoot => shoot.project_id === projectId)
      const projectTasks = tasksData.filter(task => task.project_id === projectId)
      
      setPayments(projectPayments)
      setShoots(projectShoots)
      setTasks(projectTasks)
    } catch (error: any) {
      toast.error('Failed to load project details')
      router.push('/projects')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (projectId) {
      fetchProjectData()
    }
  }, [projectId])

  const handleEditSuccess = () => {
    setIsEditModalOpen(false)
    fetchProjectData()
  }

  const handleAddShootSuccess = () => {
    setIsAddShootModalOpen(false)
    fetchProjectData()
  }

  const handleAddPaymentSuccess = () => {
    setIsAddPaymentModalOpen(false)
    fetchProjectData()
  }

  const handleAddTaskSuccess = () => {
    setIsAddTaskModalOpen(false)
    fetchProjectData()
  }

  const handleEditShootSuccess = () => {
    setEditingShoot(null)
    fetchProjectData()
  }



  const handleShootStatusChange = async (shoot: Shoot, newStatus: string) => {
    if (newStatus === 'completed') {
      // Open completion form instead of directly marking as complete
      setCompletingShoot(shoot)
      return
    }

    try {
      const updates: any = { status: newStatus }

      await shootsApi.update(shoot.id, updates)
      toast.success(`Shoot marked as ${newStatus}`)
      fetchProjectData()
    } catch (error: any) {
      toast.error(error.message || 'Failed to update shoot status')
    }
  }



  const handleCancelShoot = async (shoot: Shoot) => {
    if (!confirm(`Are you sure you want to cancel this shoot scheduled for ${new Date(shoot.scheduled_date).toLocaleDateString()}? This action can be undone by editing the shoot later.`)) {
      return
    }

    try {
      // Use the same approach as handleShootStatusChange
      await handleShootStatusChange(shoot, 'cancelled')
    } catch (error: any) {
      toast.error(error.message || 'Failed to cancel shoot')
    }
  }

  const handleCompleteShoot = async (completionData: ShootCompletionData) => {
    if (!completingShoot) return

    try {
      const updates = {
        status: 'completed',
        actual_date: new Date().toISOString(),
        device_used: completionData.device_used,
        battery_count: completionData.battery_count,
        shoot_start_time: completionData.shoot_start_time,
        shoot_end_time: completionData.shoot_end_time,
        completion_notes: completionData.completion_notes
      }

      await shootsApi.update(completingShoot.id, updates as any)
      toast.success('Shoot completed successfully')
      setCompletingShoot(null)
      fetchProjectData()
    } catch (error: any) {
      toast.error(error.message || 'Failed to complete shoot')
      throw error
    }
  }

  // Sort and filter shoots
  const getSortedAndFilteredShoots = () => {
    let filteredShoots = shoots

    // Apply status filter
    if (shootStatusFilter !== 'all') {
      filteredShoots = shoots.filter(shoot => shoot.status === shootStatusFilter)
    }

    // Apply sorting
    return filteredShoots.sort((a, b) => {
      let comparison = 0

      switch (shootSortBy) {
        case 'date':
          comparison = new Date(a.scheduled_date).getTime() - new Date(b.scheduled_date).getTime()
          break
        case 'status':
          comparison = a.status.localeCompare(b.status)
          break
        case 'amount':
          const aAmount = a.amount || 0
          const bAmount = b.amount || 0
          comparison = aAmount - bAmount
          break
        default:
          comparison = 0
      }

      return shootSortOrder === 'asc' ? comparison : -comparison
    })
  }

  const sortedAndFilteredShoots = getSortedAndFilteredShoots()

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-foreground mb-2">Project not found</h2>
          <p className="text-muted-foreground mb-4">The project you're looking for doesn't exist.</p>
          <Button onClick={() => router.push('/projects')}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Projects
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={() => router.push('/projects')}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Projects
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-foreground">{project.name}</h1>
            <div className="flex items-center space-x-2 text-muted-foreground">
              <Building className="w-4 h-4" />
              <Link href={`/clients/${project.client?.id}`} className="hover:text-blue-600">
                {project.client?.name}
              </Link>
              <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${
                project.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                project.status === 'completed' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                project.status === 'on_hold' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
              }`}>
                {project.status.replace('_', ' ')}
              </span>
              {project.location && (
                <div className="flex items-center mt-2 text-sm">
                  <MapPin className="w-4 h-4 mr-1 flex-shrink-0" />
                  <a 
                    href={formatGoogleMapsUrl(project.location)} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-primary hover:underline"
                  >
                    {project.location}
                  </a>
                </div>
              )}

            </div>
          </div>
        </div>
        <Button onClick={() => setIsEditModalOpen(true)}>
          <Edit className="w-4 h-4 mr-2" />
          Edit Project
        </Button>
      </div>

      {/* Financial Summary Card */}
      <div className="bg-card border border-border rounded-xl p-4">
        <h3 className="text-lg font-semibold text-foreground mb-4">Financial Summary</h3>

        {/* Revenue Section with Progress Bar */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <DollarSign className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-foreground">Revenue</span>
            </div>
            <span className="text-lg font-bold text-foreground">₹{project.total_amount.toLocaleString()}</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-2">
            <div
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${project.total_amount > 0 ? (project.amount_received / project.total_amount) * 100 : 0}%` }}
            ></div>
          </div>
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Received: ₹{project.amount_received.toLocaleString()}</span>
            <span>Pending: ₹{project.amount_pending.toLocaleString()}</span>
          </div>
        </div>

        {/* Costs Section */}
        <div className="grid grid-cols-3 gap-3 mb-4">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <Receipt className="w-3 h-3 text-purple-600" />
              <span className="text-xs text-muted-foreground">Expenses</span>
            </div>
            <p className="text-sm font-semibold text-foreground">₹{totalExpenses.toLocaleString()}</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <Building className="w-3 h-3 text-orange-600" />
              <span className="text-xs text-muted-foreground">Outsourcing</span>
            </div>
            <p className="text-sm font-semibold text-foreground">₹{totalOutsourcing.toLocaleString()}</p>
            {totalOutsourcing > 0 && (
              <div className="mt-1">
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                  <div
                    className="bg-green-500 h-1 rounded-full transition-all duration-300"
                    style={{ width: `${totalOutsourcing > 0 ? ((project.vendor_payment_status === 'paid' ? (project.vendor_payment_amount || 0) : 0) / totalOutsourcing) * 100 : 0}%` }}
                  ></div>
                </div>
              </div>
            )}
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center space-x-1 mb-1">
              <TrendingUp className={`w-3 h-3 ${profit >= 0 ? 'text-green-600' : 'text-red-600'}`} />
              <span className="text-xs text-muted-foreground">{profit >= 0 ? 'Profit' : 'Loss'}</span>
            </div>
            <p className={`text-sm font-semibold ${profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              ₹{Math.abs(profit).toLocaleString()}
            </p>
          </div>
        </div>

        {/* GST Info */}
        <div className="pt-3 border-t border-border">
          <p className="text-xs text-muted-foreground text-center">
            GST {project.gst_inclusive ? 'Inclusive' : 'Exclusive'}
          </p>
        </div>
      </div>

      {/* Project Stats Card */}
      <div className="bg-card border border-border rounded-lg p-6">
        <h3 className="text-lg font-semibold text-foreground mb-4">Project Statistics</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Total Shoots</span>
            <span className="font-semibold text-foreground">{shoots.length}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Completed Shoots</span>
            <span className="font-semibold text-foreground">
              {shoots.filter(s => s.status === 'completed').length}
            </span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Total Payments</span>
            <span className="font-semibold text-foreground">{payments.length}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Open Tasks</span>
            <span className="font-semibold text-foreground">
              {tasks.filter(t => t.status !== 'completed' && t.status !== 'cancelled').length}
            </span>
          </div>
        </div>
      </div>
      {/* Shoots Section */}
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-4">
          <h3 className="text-lg font-semibold text-foreground">Shoots</h3>
          <div className="flex flex-col sm:flex-row gap-3 sm:items-center">
            {/* Filters and Sorting */}
            {shoots.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {/* Status Filter */}
                <select
                  value={shootStatusFilter}
                  onChange={(e) => setShootStatusFilter(e.target.value)}
                  className="text-sm border border-border rounded-md px-3 py-1.5 bg-background focus:outline-none focus:ring-2 focus:ring-ring"
                >
                  <option value="all">All Status</option>
                  <option value="scheduled">Scheduled</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="rescheduled">Rescheduled</option>
                </select>

                {/* Sort By */}
                <select
                  value={shootSortBy}
                  onChange={(e) => setShootSortBy(e.target.value as 'date' | 'status' | 'amount')}
                  className="text-sm border border-border rounded-md px-3 py-1.5 bg-background focus:outline-none focus:ring-2 focus:ring-ring"
                >
                  <option value="date">Sort by Date</option>
                  <option value="status">Sort by Status</option>
                  <option value="amount">Sort by Amount</option>
                </select>

                {/* Sort Order */}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShootSortOrder(shootSortOrder === 'asc' ? 'desc' : 'asc')}
                  className="text-sm px-3 py-1.5 h-auto"
                >
                  <ArrowUpDown className="w-3 h-3 mr-1" />
                  {shootSortOrder === 'asc' ? 'Asc' : 'Desc'}
                </Button>
              </div>
            )}

            <Button
              onClick={() => setIsAddShootModalOpen(true)}
              size="sm"
            >
              <Plus className="w-4 h-4 mr-2" />
              Schedule Shoot
            </Button>
          </div>
        </div>
        {shoots.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No shoots scheduled for this project.</p>
          </div>
        ) : sortedAndFilteredShoots.length === 0 ? (
          <div className="text-center py-8">
            <Filter className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No shoots match the current filters.</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setShootStatusFilter('all')
                setShootSortBy('date')
                setShootSortOrder('desc')
              }}
              className="mt-2"
            >
              Clear Filters
            </Button>
          </div>
        ) : (
          <div className="space-y-3">
            {sortedAndFilteredShoots.map((shoot) => (
              <div key={shoot.id} className="border border-border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <div className="flex items-center space-x-2">
                        <span className={`w-2 h-2 rounded-full flex-shrink-0 ${
                          shoot.status === 'completed' ? 'bg-green-500' :
                          shoot.status === 'scheduled' ? 'bg-blue-500' :
                          shoot.status === 'cancelled' ? 'bg-red-500' :
                          'bg-yellow-500'
                        }`} />
                        <p className="font-medium text-foreground">
                          {new Date(shoot.scheduled_date).toLocaleDateString()}
                        </p>
                        <span className="text-sm text-muted-foreground capitalize">
                          {shoot.status}
                        </span>
                      </div>
                      {shoot.amount && (
                        <div className="text-sm font-medium text-green-600 dark:text-green-400">
                          ₹{shoot.amount.toLocaleString()}
                        </div>
                      )}
                    </div>

                    <div className="space-y-1">
                      {shoot.pilot && (
                        <p className="text-sm text-muted-foreground">
                          <User className="w-3 h-3 inline mr-1" />
                          Pilot: {shoot.pilot.name}
                        </p>
                      )}
                      {shoot.location && (
                        <p className="text-sm text-muted-foreground">
                          <MapPin className="w-3 h-3 inline mr-1" />
                          {shoot.location}
                        </p>
                      )}
                      {shoot.notes && (
                        <p className="text-sm text-muted-foreground line-clamp-2">{shoot.notes}</p>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-2 ml-4 flex-shrink-0">
                    {/* Always visible core actions */}
                    <div className="flex items-center space-x-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setEditingShoot(shoot)}
                        className="text-xs text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-950"
                        title="Edit shoot"
                      >
                        <Edit className="w-3 h-3" />
                      </Button>
                    </div>

                    {/* Desktop buttons - Additional actions */}
                    <div className="hidden md:flex items-center space-x-1">
                      {shoot.status === 'scheduled' && (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleShootStatusChange(shoot, 'completed')}
                            className="text-xs text-green-600 hover:text-green-700 dark:text-green-400 dark:hover:text-green-300 hover:bg-green-50 dark:hover:bg-green-950"
                          >
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Complete
                          </Button>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleCancelShoot(shoot)}
                            className="text-xs text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-950"
                          >
                            <X className="w-3 h-3 mr-1" />
                            Cancel
                          </Button>
                        </>
                      )}
                    </div>

                    {/* Mobile dropdown menu - Only show for scheduled shoots with additional actions */}
                    {shoot.status === 'scheduled' && (
                      <div className="sm:hidden">
                        <DropdownMenu
                          trigger={
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 w-8 p-0"
                              title="More actions"
                            >
                              <MoreVertical className="w-4 h-4" />
                            </Button>
                          }
                        >
                          <DropdownMenuItem onClick={() => handleShootStatusChange(shoot, 'completed')}>
                            <CheckCircle className="w-4 h-4 mr-2 text-green-600" />
                            Mark Complete
                          </DropdownMenuItem>

                          <DropdownMenuItem
                            onClick={() => handleCancelShoot(shoot)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                          >
                            <X className="w-4 h-4 mr-2" />
                            Cancel Shoot
                          </DropdownMenuItem>
                        </DropdownMenu>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Shoots Summary */}
        {shoots.length > 0 && (
          <div className="mt-6 pt-4 border-t border-border">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-foreground">
                  {sortedAndFilteredShoots.length}
                </div>
                <div className="text-sm text-muted-foreground">
                  {shootStatusFilter === 'all' ? 'Total Shoots' : `${shootStatusFilter} Shoots`}
                </div>
              </div>
              <div>
                <div className="text-lg font-semibold text-green-600 dark:text-green-400">
                  {sortedAndFilteredShoots.filter(s => s.status === 'completed').length}
                </div>
                <div className="text-sm text-muted-foreground">Completed</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                  {sortedAndFilteredShoots.filter(s => s.status === 'scheduled').length}
                </div>
                <div className="text-sm text-muted-foreground">Scheduled</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-yellow-600 dark:text-yellow-400">
                  ₹{sortedAndFilteredShoots
                    .filter(s => s.amount)
                    .reduce((sum, s) => sum + (s.amount || 0), 0)
                    .toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Total Amount</div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Expenses Section */}
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-foreground">Project Expenses</h3>
          <Button
            onClick={() => setIsAddExpenseModalOpen(true)}
            size="sm"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Expense
          </Button>
        </div>
        {expensesLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : expensesError ? (
          <div className="text-center py-8 text-red-500">
            <AlertCircle className="w-8 h-8 mx-auto mb-2" />
            <p>Error loading expenses: {expensesError}</p>
          </div>
        ) : expenses.length === 0 ? (
          <div className="text-center py-8">
            <Receipt className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No expenses recorded for this project.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {expenses.map((expense) => (
              <ExpenseCard key={expense.id} expense={expense} />
            ))}
          </div>
        )}
      </div>

      {/* Payments Section */}
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-foreground">Payments</h3>
          <Button
            onClick={() => setIsAddPaymentModalOpen(true)}
            size="sm"
          >
            <Plus className="w-4 h-4 mr-2" />
            Record Payment
          </Button>
        </div>
        {payments.length === 0 ? (
          <div className="text-center py-8">
            <DollarSign className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No payments recorded for this project.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {payments
              .sort((a, b) => new Date(b.payment_date).getTime() - new Date(a.payment_date).getTime())
              .map((payment) => (
                <div key={payment.id} className="border border-border rounded-lg p-4 hover:bg-muted/50">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-foreground">₹{payment.amount.toLocaleString()}</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(payment.payment_date).toLocaleDateString()} •
                        <span className="capitalize ml-1">
                          {payment.payment_method.replace('_', ' ')}
                        </span>
                      </p>
                      {payment.reference_number && (
                        <p className="text-xs text-muted-foreground">Ref: {payment.reference_number}</p>
                      )}
                      {payment.notes && (
                        <p className="text-xs text-muted-foreground mt-1">{payment.notes}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
          </div>
        )}
      </div>

      {/* Vendor Payment Section */}
      {project.vendor_payment_amount && project.vendor_payment_amount > 0 && (
        <div className="bg-card border border-border rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-foreground">Vendor Payment</h3>
            <Button
              onClick={() => setIsVendorPaymentModalOpen(true)}
              size="sm"
              variant="outline"
            >
              <Edit className="w-4 h-4 mr-2" />
              Update Payment
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {/* Payment Amount */}
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <DollarSign className="w-4 h-4 text-blue-600" />
                <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Amount</span>
              </div>
              <div className="text-xl font-bold text-foreground">
                ₹{project.vendor_payment_amount.toLocaleString()}
              </div>
            </div>

            {/* Payment Status */}
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Status</span>
              </div>
              <div className={`inline-flex items-center px-2 py-1 rounded-full text-sm font-medium ${
                project.vendor_payment_status === 'paid' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                project.vendor_payment_status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                project.vendor_payment_status === 'overdue' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
              }`}>
                {project.vendor_payment_status === 'paid' ? '✓ Paid' :
                 project.vendor_payment_status === 'pending' ? '⏳ Pending' :
                 project.vendor_payment_status === 'overdue' ? '⚠️ Overdue' :
                 '❌ Cancelled'}
              </div>
            </div>

            {/* Due Date */}
            {project.vendor_payment_due_date && (
              <div className="bg-muted/50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <Clock className="w-4 h-4 text-orange-600" />
                  <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Due Date</span>
                </div>
                <div className="text-sm font-medium text-foreground">
                  {new Date(project.vendor_payment_due_date).toLocaleDateString()}
                </div>
              </div>
            )}

            {/* Payment Date */}
            {project.vendor_payment_date && (
              <div className="bg-muted/50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <Calendar className="w-4 h-4 text-green-600" />
                  <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Paid On</span>
                </div>
                <div className="text-sm font-medium text-foreground">
                  {new Date(project.vendor_payment_date).toLocaleDateString()}
                </div>
              </div>
            )}
          </div>

          {/* Payment Notes */}
          {project.vendor_payment_notes && (
            <div className="border border-border rounded-lg p-4 bg-muted/30">
              <h4 className="text-sm font-medium text-foreground mb-2">Payment Notes</h4>
              <p className="text-sm text-muted-foreground">{project.vendor_payment_notes}</p>
            </div>
          )}
        </div>
      )}

      {/* Tasks Section */}
      <div className="bg-card border border-border rounded-lg p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-foreground">Tasks</h3>
          <Button
            onClick={() => setIsAddTaskModalOpen(true)}
            size="sm"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Task
          </Button>
        </div>
        {tasks.length === 0 ? (
          <div className="text-center py-8">
            <CheckCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No tasks created for this project.</p>
          </div>
        ) : (
          <div className="space-y-3">
            {tasks.map((task) => (
              <div key={task.id} className="border border-border rounded-lg p-4 hover:bg-muted/50">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      {task.status === 'completed' ? (
                        <CheckCircle className="w-4 h-4 text-green-500" />
                      ) : task.status === 'in_progress' ? (
                        <Clock className="w-4 h-4 text-blue-500" />
                      ) : task.status === 'cancelled' ? (
                        <AlertCircle className="w-4 h-4 text-red-500" />
                      ) : (
                        <AlertCircle className="w-4 h-4 text-gray-500" />
                      )}
                      <p className="font-medium text-foreground">{task.title}</p>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${
                        task.priority === 'urgent' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                        task.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400' :
                        task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                        'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                      }`}>
                        {task.priority}
                      </span>
                    </div>
                    {task.description && (
                      <p className="text-sm text-muted-foreground mt-1">{task.description}</p>
                    )}
                    <div className="flex items-center space-x-4 mt-2 text-xs text-muted-foreground">
                      {task.assigned_user && (
                        <div className="flex items-center">
                          <User className="w-3 h-3 mr-1" />
                          {task.assigned_user.name}
                        </div>
                      )}
                      {task.due_date && (
                        <div className="flex items-center">
                          <Calendar className="w-3 h-3 mr-1" />
                          Due: {new Date(task.due_date).toLocaleDateString()}
                        </div>
                      )}
                      <span className="capitalize">{task.status.replace('_', ' ')}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modals */}
      {/* Edit Project Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Project"
        size="lg"
      >
        <ProjectForm
          project={project}
          onSuccess={handleEditSuccess}
          onCancel={() => setIsEditModalOpen(false)}
        />
      </Modal>

      {/* Add Shoot Modal */}
      <Modal
        isOpen={isAddShootModalOpen}
        onClose={() => setIsAddShootModalOpen(false)}
        title="Schedule New Shoot"
        size="xl"
      >
        <ShootForm
          onSuccess={handleAddShootSuccess}
          onCancel={() => setIsAddShootModalOpen(false)}
          preselectedProjectId={projectId}
        />
      </Modal>

      {/* Edit Shoot Modal */}
      <Modal
        isOpen={!!editingShoot}
        onClose={() => setEditingShoot(null)}
        title="Edit Shoot"
        size="xl"
      >
        {editingShoot && (
          <ShootForm
            shoot={editingShoot}
            onSuccess={handleEditShootSuccess}
            onCancel={() => setEditingShoot(null)}
          />
        )}
      </Modal>

      {/* Add Payment Modal */}
      <Modal
        isOpen={isAddPaymentModalOpen}
        onClose={() => setIsAddPaymentModalOpen(false)}
        title="Record Payment"
        size="lg"
      >
        <PaymentForm
          onSuccess={handleAddPaymentSuccess}
          onCancel={() => setIsAddPaymentModalOpen(false)}
          preselectedProjectId={projectId}
        />
      </Modal>

      {/* Add Task Modal */}
      <Modal
          isOpen={isAddTaskModalOpen}
          onClose={() => setIsAddTaskModalOpen(false)}
          title="Add New Task"
          size="lg"
        >
          <TaskForm
            onSuccess={handleAddTaskSuccess}
            onCancel={() => setIsAddTaskModalOpen(false)}
            preselectedProjectId={projectId}
          />
        </Modal>

        {/* Add Expense Modal */}
        <Modal
          isOpen={isAddExpenseModalOpen}
          onClose={() => setIsAddExpenseModalOpen(false)}
          title="Add Project Expense"
          size="lg"
        >
          <AddExpenseForm
            projectId={project.id}
            onSuccess={() => {
              setIsAddExpenseModalOpen(false);
              refetchExpenses();
              toast.success("Expense added successfully");
            }}
            onCancel={() => setIsAddExpenseModalOpen(false)}
          />
        </Modal>

        {/* Vendor Payment Modal */}
        <Modal
          isOpen={isVendorPaymentModalOpen}
          onClose={() => setIsVendorPaymentModalOpen(false)}
          title="Manage Vendor Payment"
          size="lg"
        >
          <VendorPaymentForm
            project={project}
            onSuccess={() => {
              setIsVendorPaymentModalOpen(false)
              fetchProjectData()
            }}
            onCancel={() => setIsVendorPaymentModalOpen(false)}
          />
        </Modal>

        {/* Shoot Completion Form */}
        <ShootCompletionForm
          shoot={completingShoot}
          isOpen={!!completingShoot}
          onClose={() => setCompletingShoot(null)}
          onComplete={handleCompleteShoot}
        />
    </div>
  )
}
