'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import {
  LayoutDashboard,
  Users,
  FolderOpen,
  Calendar,
  DollarSign,
  CheckSquare,
  CreditCard,
  Map,
  Settings,
  LogOut,
  Menu,
  ChevronLeft,
  Building2
} from 'lucide-react'
import toast from 'react-hot-toast'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { name: 'Clients', href: '/clients', icon: Users },
  { name: 'Vendors', href: '/vendors', icon: Building2 },
  { name: 'Projects', href: '/projects', icon: FolderOpen },
  { name: 'Shoots', href: '/shoots', icon: Calendar },
  { name: 'Finances', href: '/finances', icon: DollarSign },
  { name: 'Tasks', href: '/tasks', icon: CheckSquare },
  { name: 'Map', href: '/map', icon: Map },
  { name: 'Settings', href: '/settings', icon: Settings },
]

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, loading, signOut } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const [isExpanded, setIsExpanded] = useState(false)

  // Load sidebar state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem('sidebar-expanded')
    if (savedState !== null) {
      setIsExpanded(JSON.parse(savedState))
    }
  }, [])

  // Save sidebar state to localStorage when it changes
  const toggleSidebar = () => {
    const newState = !isExpanded
    setIsExpanded(newState)
    localStorage.setItem('sidebar-expanded', JSON.stringify(newState))
  }

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  const handleSignOut = async () => {
    try {
      await signOut()
      toast.success('Signed out successfully')
      router.push('/login')
    } catch (error: any) {
      toast.error(error.message || 'Failed to sign out')
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" suppressHydrationWarning={true}>
        <div className="text-center" suppressHydrationWarning={true}>
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" suppressHydrationWarning={true}></div>
          <p className="mt-4 text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-background" suppressHydrationWarning={true}>
      {/* Sidebar */}
      <div className={`fixed left-0 top-0 ${isExpanded ? 'w-64' : 'w-16'} bg-card shadow-sm border-r border-border transition-all duration-300 flex flex-col h-screen z-30 group/sidebar`}>
        {/* Header */}
        <div className={`${isExpanded ? 'p-6' : 'p-4'} border-b border-border flex-shrink-0`}>
          <div className="flex items-center justify-between">
            {isExpanded ? (
              <div>
                <h1 className="text-xl font-bold text-card-foreground">Cymatics</h1>
                <p className="text-sm text-muted-foreground">Drone Service Management</p>
              </div>
            ) : (
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">C</span>
              </div>
            )}
          </div>
        </div>

        {/* Arrow Toggle Button */}
        <button
          onClick={toggleSidebar}
          className={`absolute top-4 ${isExpanded ? '-right-1.5' : '-right-1.5'}
            w-3 h-6 sm:w-3.5 sm:h-7 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-r-sm shadow-lg
            hover:bg-gray-50 dark:hover:bg-gray-700 hover:shadow-xl transition-all duration-300 z-40
            flex items-center justify-center group
            opacity-0 group-hover/sidebar:opacity-100 translate-x-2 group-hover/sidebar:translate-x-0`}
          aria-label={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
        >
          <ChevronLeft
            className={`w-2.5 h-2.5 sm:w-3 sm:h-3 text-gray-600 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-transform duration-200 ${
              isExpanded ? '' : 'rotate-180'
            }`}
          />
        </button>

        {/* Navigation - Flexible area */}
        <nav className="mt-6 flex-1 overflow-y-auto">
          <div className={`${isExpanded ? 'px-3' : 'px-2'}`}>
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`flex items-center ${isExpanded ? 'px-3 py-2' : 'px-2 py-3 justify-center'} text-sm font-medium rounded-md mb-1 transition-colors ${
                    isActive
                      ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'text-muted-foreground hover:bg-muted hover:text-foreground'
                  }`}
                  title={!isExpanded ? item.name : undefined}
                >
                  <item.icon className={`w-5 h-5 ${isExpanded ? 'mr-3' : ''}`} />
                  {isExpanded && item.name}
                </Link>
              )
            })}
          </div>
        </nav>

        {/* User Info - Fixed at bottom */}
        <div className="p-4 border-t border-border flex-shrink-0">
          {isExpanded ? (
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                <span className="text-blue-600 dark:text-blue-400 font-medium text-sm">
                  {user.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-card-foreground truncate">{user.name}</p>
                <p className="text-xs text-muted-foreground capitalize">{user.role}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSignOut}
                className="p-2"
              >
                <LogOut className="w-4 h-4" />
              </Button>
            </div>
          ) : (
            <div className="flex flex-col items-center space-y-2">
              <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                <span className="text-blue-600 dark:text-blue-400 font-medium text-sm">
                  {user.name.charAt(0).toUpperCase()}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSignOut}
                className="p-2"
                title="Sign Out"
              >
                <LogOut className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className={`${isExpanded ? 'ml-64' : 'ml-16'} transition-all duration-300 min-h-screen bg-background`}>
        <main className="p-8">
          {children}
        </main>
      </div>
    </div>
  )
}
