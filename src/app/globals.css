@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #0f172a;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --popover: #ffffff;
  --popover-foreground: #0f172a;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f1f5f9;
  --muted-foreground: #475569;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #3b82f6;
  --radius: 0.5rem;
}

.dark {
  --background: #000000;
  --foreground: #ffffff;
  --card: #111111;
  --card-foreground: #ffffff;
  --popover: #111111;
  --popover-foreground: #ffffff;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #1a1a1a;
  --secondary-foreground: #ffffff;
  --muted: #1a1a1a;
  --muted-foreground: #a3a3a3;
  --accent: #1a1a1a;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #333333;
  --input: #333333;
  --ring: #3b82f6;
}

* {
  border-color: var(--border);
}

body {
  background-color: var(--background);
  color: var(--foreground);
}

/* Ensure text visibility in both themes */
.text-muted-foreground {
  color: var(--muted-foreground) !important;
}

.text-foreground {
  color: var(--foreground) !important;
}

.text-card-foreground {
  color: var(--card-foreground) !important;
}

/* Ensure proper contrast for colored text on colored backgrounds */
.text-green-800 {
  color: #166534 !important;
}

.text-red-800 {
  color: #991b1b !important;
}

.text-blue-800 {
  color: #1e40af !important;
}

.text-yellow-800 {
  color: #92400e !important;
}

.text-orange-800 {
  color: #9a3412 !important;
}

.text-green-900 {
  color: #14532d !important;
}

.text-red-900 {
  color: #7f1d1d !important;
}

.text-blue-900 {
  color: #1e3a8a !important;
}

.text-orange-900 {
  color: #7c2d12 !important;
}

/* Override any remaining hardcoded colors for dark theme compatibility */
.dark .text-gray-600 {
  color: var(--muted-foreground) !important;
}

.dark .text-gray-700 {
  color: var(--foreground) !important;
}

.dark .text-gray-800 {
  color: var(--foreground) !important;
}

.dark .text-gray-900 {
  color: var(--foreground) !important;
}

.dark .bg-gray-50 {
  background-color: var(--muted) !important;
}

.dark .bg-gray-100 {
  background-color: var(--muted) !important;
}

.dark .bg-gray-200 {
  background-color: var(--border) !important;
}

.dark .bg-white {
  background-color: var(--card) !important;
}

.dark .border-gray-100 {
  border-color: var(--border) !important;
}

.dark .border-gray-200 {
  border-color: var(--border) !important;
}

.dark .border-gray-300 {
  border-color: var(--border) !important;
}

/* Override colored text for better dark mode visibility */
.dark .text-green-600 {
  color: #4ade80 !important; /* green-400 */
}

.dark .text-orange-600 {
  color: #fb923c !important; /* orange-400 */
}

.dark .text-red-600 {
  color: #f87171 !important; /* red-400 */
}

.dark .text-blue-600 {
  color: #60a5fa !important; /* blue-400 */
}

.dark .text-yellow-600 {
  color: #facc15 !important; /* yellow-400 */
}

.dark .text-purple-600 {
  color: #c084fc !important; /* purple-400 */
}

.dark .text-pink-600 {
  color: #f472b6 !important; /* pink-400 */
}

.dark .text-indigo-600 {
  color: #818cf8 !important; /* indigo-400 */
}

/* Override hover states for colored text - using simpler approach */
.dark .text-green-600:hover {
  color: #22c55e !important; /* green-500 */
}

.dark .text-orange-600:hover {
  color: #f97316 !important; /* orange-500 */
}

.dark .text-red-600:hover {
  color: #ef4444 !important; /* red-500 */
}

.dark .text-blue-600:hover {
  color: #3b82f6 !important; /* blue-500 */
}

/* Override colored backgrounds for dark mode - subtle, elegant backgrounds */
.dark .bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.1) !important; /* blue-500/10 */
}

.dark .bg-orange-50 {
  background-color: rgba(249, 115, 22, 0.1) !important; /* orange-500/10 */
}

.dark .bg-green-50 {
  background-color: rgba(34, 197, 94, 0.1) !important; /* green-500/10 */
}

.dark .bg-red-50 {
  background-color: rgba(239, 68, 68, 0.1) !important; /* red-500/10 */
}

.dark .bg-yellow-50 {
  background-color: rgba(234, 179, 8, 0.1) !important; /* yellow-500/10 */
}

/* Subtle colored backgrounds for elegant dark theme */
.dark .bg-green-950\/50 {
  background-color: rgba(5, 46, 22, 0.5) !important; /* Very dark green with opacity */
}

.dark .bg-red-950\/50 {
  background-color: rgba(69, 10, 10, 0.5) !important; /* Very dark red with opacity */
}

.dark .bg-blue-950\/50 {
  background-color: rgba(23, 37, 84, 0.5) !important; /* Very dark blue with opacity */
}

.dark .bg-orange-950\/50 {
  background-color: rgba(67, 20, 7, 0.5) !important; /* Very dark orange with opacity */
}

.dark .bg-yellow-950\/50 {
  background-color: rgba(66, 32, 6, 0.5) !important; /* Very dark yellow with opacity */
}

/* Subtle borders for elegant appearance */
.dark .border-green-800\/30 {
  border-color: rgba(22, 101, 52, 0.3) !important;
}

.dark .border-red-800\/30 {
  border-color: rgba(153, 27, 27, 0.3) !important;
}

.dark .border-blue-800\/30 {
  border-color: rgba(30, 64, 175, 0.3) !important;
}

.dark .border-orange-800\/30 {
  border-color: rgba(154, 52, 18, 0.3) !important;
}

/* Override dark text colors that are invisible in dark mode */
.dark .text-green-700 {
  color: #4ade80 !important; /* green-400 */
}

.dark .text-green-800 {
  color: #22c55e !important; /* green-500 */
}

.dark .text-green-900 {
  color: #16a34a !important; /* green-600 */
}

.dark .text-red-700 {
  color: #f87171 !important; /* red-400 */
}

.dark .text-red-800 {
  color: #ef4444 !important; /* red-500 */
}

.dark .text-red-900 {
  color: #dc2626 !important; /* red-600 */
}

.dark .text-blue-700 {
  color: #60a5fa !important; /* blue-400 */
}

.dark .text-blue-800 {
  color: #3b82f6 !important; /* blue-500 */
}

.dark .text-blue-900 {
  color: #2563eb !important; /* blue-600 */
}

.dark .text-orange-700 {
  color: #fb923c !important; /* orange-400 */
}

.dark .text-orange-800 {
  color: #f97316 !important; /* orange-500 */
}

.dark .text-orange-900 {
  color: #ea580c !important; /* orange-600 */
}

.dark .text-yellow-700 {
  color: #facc15 !important; /* yellow-400 */
}

.dark .text-yellow-800 {
  color: #eab308 !important; /* yellow-500 */
}

.dark .text-yellow-900 {
  color: #ca8a04 !important; /* yellow-600 */
}
