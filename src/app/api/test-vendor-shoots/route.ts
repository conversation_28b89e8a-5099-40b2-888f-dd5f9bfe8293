import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/auth-server'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const vendorId = searchParams.get('vendorId') || 'fa2e2c4c-7bff-47e5-927c-044c15bd04bb'
    
    const supabase = createServerSupabaseClient()
    
    console.log('Testing vendor shoots query for vendor:', vendorId)
    
    // Test the exact query used by the vendor details page
    const { data: shootsData, error: shootsError } = await supabase
      .from('shoots')
      .select(`
        *,
        project:projects(
          *,
          client:clients(name)
        )
      `)
      .eq('vendor_id', vendorId)
      .eq('is_outsourced', true)
      .order('scheduled_date', { ascending: false })

    console.log('Query result:', { shootsData, shootsError })
    
    if (shootsError) {
      console.error('Supabase error:', shootsError)
      return NextResponse.json({ error: shootsError }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      vendorId,
      shootsCount: shootsData?.length || 0,
      shoots: shootsData
    })
    
  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
