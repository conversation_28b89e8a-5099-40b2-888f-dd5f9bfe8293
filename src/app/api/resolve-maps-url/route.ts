import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json()

    if (!url || typeof url !== 'string') {
      return NextResponse.json(
        { error: 'URL is required' },
        { status: 400 }
      )
    }

    // Validate that it's a Google Maps URL
    const isValidGoogleMapsUrl = /^https?:\/\/(maps\.app\.goo\.gl|goo\.gl\/maps|maps\.google\.|google\..*\/maps)/.test(url)
    
    if (!isValidGoogleMapsUrl) {
      return NextResponse.json(
        { error: 'Invalid Google Maps URL' },
        { status: 400 }
      )
    }

    // Try to resolve the URL by following redirects
    try {
      // First try HEAD request
      const headResponse = await fetch(url, {
        method: 'HEAD',
        redirect: 'follow',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
      })

      let resolvedUrl = headResponse.url

      // If we got a different URL, it means we followed redirects
      if (resolvedUrl && resolvedUrl !== url && resolvedUrl.includes('google.com/maps')) {
        return NextResponse.json({ resolvedUrl })
      }

      // If HEAD didn't work or didn't give us a Google Maps URL, try GET
      const getResponse = await fetch(url, {
        method: 'GET',
        redirect: 'follow',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'DNT': '1',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1'
        }
      })

      resolvedUrl = getResponse.url
      if (resolvedUrl && resolvedUrl !== url) {
        return NextResponse.json({ resolvedUrl })
      }

      // If still no luck, return the original URL
      return NextResponse.json({ resolvedUrl: url })

    } catch (fetchError) {
      console.error('Error fetching URL:', fetchError)
      
      // If fetch fails, try to extract from the URL structure itself
      // For maps.app.goo.gl URLs, we can sometimes get info from the path
      if (url.includes('maps.app.goo.gl')) {
        // These URLs typically have a pattern, but without resolving we can't extract location
        return NextResponse.json(
          { error: 'Unable to resolve shortened URL. Please use the full Google Maps URL.' },
          { status: 422 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to resolve URL' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error in resolve-maps-url API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle preflight requests for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
