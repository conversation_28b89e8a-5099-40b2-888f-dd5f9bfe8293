import { useState, useEffect } from 'react'
import {
  clientsApi,
  contact<PERSON>ersonsApi,
  projectsApi,
  shootsApi,
  expensesApi,
  tasksApi,
  paymentsApi,
  dashboardApi
} from '@/lib/api'
import type {
  Client,
  ContactPerson,
  Project,
  Shoot,
  Expense,
  Task,
  Payment,
  DashboardStats
} from '@/types'

// Generic hook for API calls
function useApiCall<T>(apiCall: () => Promise<T>, dependencies: any[] = []) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const refetch = async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await apiCall()
      setData(result)
    } catch (err: any) {
      setError(err.message || 'An error occurred')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    refetch()
  }, dependencies)

  return { data, loading, error, refetch }
}

// Clients hooks
export function useClients() {
  return useApiCall(() => clientsApi.getAll())
}

export function useClient(id: string) {
  return useApiCall(() => clientsApi.getById(id), [id])
}

// Contact Persons hooks
export function useContactPersons(clientId: string) {
  return useApiCall(() => contactPersonsApi.getByClientId(clientId), [clientId])
}

// Projects hooks
export function useProjects() {
  return useApiCall(() => projectsApi.getAll())
}

export function useProject(id: string) {
  return useApiCall(() => projectsApi.getById(id), [id])
}

// Shoots hooks
export function useShoots() {
  return useApiCall(() => shootsApi.getAll())
}

export function useUpcomingShoots() {
  return useApiCall(() => shootsApi.getUpcoming())
}

// Tasks hooks
export function useTasks() {
  return useApiCall(() => tasksApi.getAll())
}

export function useMyTasks() {
  return useApiCall(() => tasksApi.getMyTasks())
}

// Dashboard hooks
export function useDashboardStats() {
  return useApiCall(() => dashboardApi.getStats())
}

// Mutation hooks for create/update/delete operations
export function useCreateClient() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createClient = async (clientData: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await clientsApi.create(clientData)
      return result
    } catch (err: any) {
      setError(err.message || 'Failed to create client')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { createClient, loading, error }
}

export function useUpdateClient() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateClient = async (id: string, updates: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await clientsApi.update(id, updates)
      return result
    } catch (err: any) {
      setError(err.message || 'Failed to update client')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { updateClient, loading, error }
}

export function useDeleteClient() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deleteClient = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      await clientsApi.delete(id)
    } catch (err: any) {
      setError(err.message || 'Failed to delete client')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { deleteClient, loading, error }
}

export function useCreateProject() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createProject = async (projectData: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await projectsApi.create(projectData)
      return result
    } catch (err: any) {
      setError(err.message || 'Failed to create project')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { createProject, loading, error }
}

export function useUpdateProject() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateProject = async (id: string, updates: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await projectsApi.update(id, updates)
      return result
    } catch (err: any) {
      setError(err.message || 'Failed to update project')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { updateProject, loading, error }
}

export function useCreateShoot() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createShoot = async (shootData: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await shootsApi.create(shootData)
      return result
    } catch (err: any) {
      setError(err.message || 'Failed to create shoot')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { createShoot, loading, error }
}

export function useUpdateShoot() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateShoot = async (id: string, updates: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await shootsApi.update(id, updates)
      return result
    } catch (err: any) {
      setError(err.message || 'Failed to update shoot')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { updateShoot, loading, error }
}

export function useCreateExpense() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createExpense = async (expenseData: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await expensesApi.create(expenseData)
      return result
    } catch (err: any) {
      setError(err.message || 'Failed to create expense')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { createExpense, loading, error }
}

export function useCreateTask() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createTask = async (taskData: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await tasksApi.create(taskData)
      return result
    } catch (err: any) {
      setError(err.message || 'Failed to create task')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { createTask, loading, error }
}

export function useUpdateTask() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateTask = async (id: string, updates: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await tasksApi.update(id, updates)
      return result
    } catch (err: any) {
      setError(err.message || 'Failed to update task')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { updateTask, loading, error }
}

export function usePayments() {
  const [data, setData] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchPayments = async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await paymentsApi.getAll()
      setData(result)
    } catch (err: any) {
      setError(err.message || 'Failed to fetch payments')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPayments()
  }, [])

  return { data, loading, error, refetch: fetchPayments }
}

export function useExpenses() {
  const [data, setData] = useState<Expense[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchExpenses = async () => {
    try {
      setLoading(true)
      setError(null)
      const result = await expensesApi.getAll()
      setData(result)
    } catch (err: any) {
      setError(err.message || 'Failed to fetch expenses')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchExpenses()
  }, [])

  return { data, loading, error, refetch: fetchExpenses }
}

export function useCreatePayment() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createPayment = async (paymentData: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await paymentsApi.create(paymentData)
      return result
    } catch (err: any) {
      setError(err.message || 'Failed to create payment')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { createPayment, loading, error }
}

export function useUpdatePayment() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updatePayment = async (id: string, updates: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await paymentsApi.update(id, updates)
      return result
    } catch (err: any) {
      setError(err.message || 'Failed to update payment')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { updatePayment, loading, error }
}

export function useUpdateExpense() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const updateExpense = async (id: string, updates: any) => {
    try {
      setLoading(true)
      setError(null)
      const result = await expensesApi.update(id, updates)
      return result
    } catch (err: any) {
      setError(err.message || 'Failed to update expense')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { updateExpense, loading, error }
}

export function useDeletePayment() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deletePayment = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      await paymentsApi.delete(id)
    } catch (err: any) {
      setError(err.message || 'Failed to delete payment')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { deletePayment, loading, error }
}

export function useDeleteExpense() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deleteExpense = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      await expensesApi.delete(id)
    } catch (err: any) {
      setError(err.message || 'Failed to delete expense')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { deleteExpense, loading, error }
}

export function useDeleteProject() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deleteProject = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      await projectsApi.delete(id)
    } catch (err: any) {
      setError(err.message || 'Failed to delete project')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { deleteProject, loading, error }
}

export function useDeleteShoot() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deleteShoot = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      await shootsApi.delete(id)
    } catch (err: any) {
      setError(err.message || 'Failed to delete shoot')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { deleteShoot, loading, error }
}

export function useDeleteTask() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const deleteTask = async (id: string) => {
    try {
      setLoading(true)
      setError(null)
      await tasksApi.delete(id)
    } catch (err: any) {
      setError(err.message || 'Failed to delete task')
      throw err
    } finally {
      setLoading(false)
    }
  }

  return { deleteTask, loading, error }
}
