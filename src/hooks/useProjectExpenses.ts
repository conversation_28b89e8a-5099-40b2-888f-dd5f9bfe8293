import { useState, useEffect } from 'react'
import { Expense } from '../types'
import { expensesApi } from '../lib/api'

export function useProjectExpenses(projectId: string | null) {
  const [expenses, setExpenses] = useState<Expense[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchProjectExpenses() {
      if (!projectId) return
      
      setLoading(true)
      setError(null)
      
      try {
        const data = await expensesApi.getByProjectId(projectId)
        setExpenses(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch expenses')
        console.error('Error fetching project expenses:', err)
      } finally {
        setLoading(false)
      }
    }
    
    fetchProjectExpenses()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectId])

  const refetch = async () => {
    if (!projectId) return
    setLoading(true)
    setError(null)
    
    try {
      const data = await expensesApi.getByProjectId(projectId)
      setExpenses(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch expenses')
      console.error('Error fetching project expenses:', err)
    } finally {
      setLoading(false)
    }
  }

  return { expenses, loading, error, refetch }
}